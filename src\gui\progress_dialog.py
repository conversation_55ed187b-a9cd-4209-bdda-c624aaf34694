# Progress Dialog for long-running operations
import tkinter as tk
from tkinter import ttk
import threading
import time
from typing import Optional, Callable, Any
from datetime import datetime, timedelta

from ..utils.logging_config import get_logger

logger = get_logger("gui.progress_dialog")


class ProgressDialog:
    """Modal dialog for displaying progress of long-running operations."""
    
    def __init__(self, parent, title: str = "Progress", 
                 can_cancel: bool = True, 
                 on_cancel: Optional[Callable] = None):
        self.parent = parent
        self.title = title
        self.can_cancel = can_cancel
        self.on_cancel = on_cancel
        
        # Progress state
        self.current_value = 0
        self.maximum_value = 100
        self.status_text = "Starting..."
        self.start_time = datetime.now()
        self.cancelled = False
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog on parent
        self._center_dialog()
        
        # Setup UI
        self._setup_ui()
        
        # Start update loop
        self._schedule_updates()
        
        logger.debug(f"ProgressDialog created: {title}")
    
    def _center_dialog(self):
        """Center the dialog on the parent window."""
        self.dialog.update_idletasks()
        
        # Get parent geometry
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Calculate center position
        dialog_width = self.dialog.winfo_reqwidth()
        dialog_height = self.dialog.winfo_reqheight()
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
    
    def _setup_ui(self):
        """Setup the dialog UI."""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title label
        self.title_label = ttk.Label(main_frame, text=self.title, 
                                   font=("TkDefaultFont", 12, "bold"))
        self.title_label.pack(pady=(0, 10))
        
        # Status label
        self.status_label = ttk.Label(main_frame, text=self.status_text)
        self.status_label.pack(pady=(0, 10))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            main_frame, 
            variable=self.progress_var,
            maximum=self.maximum_value,
            length=400,
            mode='determinate'
        )
        self.progress_bar.pack(pady=(0, 10))
        
        # Progress info frame
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # Progress percentage and count
        progress_info_frame = ttk.Frame(info_frame)
        progress_info_frame.pack(side=tk.LEFT)

        self.percentage_label = ttk.Label(progress_info_frame, text="0%",
                                        font=("TkDefaultFont", 10, "bold"))
        self.percentage_label.pack(side=tk.LEFT)

        self.count_label = ttk.Label(progress_info_frame, text="(0/0)",
                                   font=("TkDefaultFont", 9))
        self.count_label.pack(side=tk.LEFT, padx=(5, 0))

        # Speed indicator
        self.speed_label = ttk.Label(info_frame, text="",
                                   font=("TkDefaultFont", 9), foreground="blue")
        self.speed_label.pack(side=tk.LEFT, padx=(20, 0))

        # Time info frame
        time_info_frame = ttk.Frame(info_frame)
        time_info_frame.pack(side=tk.RIGHT)

        # ETA label
        self.eta_label = ttk.Label(time_info_frame, text="ETA: Calculating...",
                                 font=("TkDefaultFont", 9, "bold"), foreground="green")
        self.eta_label.pack(side=tk.RIGHT)

        # Elapsed time label
        self.elapsed_label = ttk.Label(time_info_frame, text="Elapsed: 00:00:00",
                                     font=("TkDefaultFont", 9))
        self.elapsed_label.pack(side=tk.RIGHT, padx=(0, 20))
        
        # Details text (expandable)
        self.details_frame = ttk.LabelFrame(main_frame, text="Details")
        self.details_text = tk.Text(self.details_frame, height=4, width=50, 
                                  wrap=tk.WORD, state=tk.DISABLED)
        details_scroll = ttk.Scrollbar(self.details_frame, orient=tk.VERTICAL, 
                                     command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=details_scroll.set)
        
        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        details_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Initially hide details
        self.details_visible = False
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Show/Hide details button
        self.details_button = ttk.Button(button_frame, text="Show Details", 
                                       command=self._toggle_details)
        self.details_button.pack(side=tk.LEFT)
        
        # Cancel button
        if self.can_cancel:
            self.cancel_button = ttk.Button(button_frame, text="Cancel", 
                                          command=self._on_cancel)
            self.cancel_button.pack(side=tk.RIGHT)
        
        # Handle dialog close
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_close)
    
    def _schedule_updates(self):
        """Schedule periodic UI updates."""
        if not self.cancelled and self.dialog.winfo_exists():
            self._update_display()
            self.dialog.after(100, self._schedule_updates)
    
    def _update_display(self):
        """Update the progress display."""
        # Update progress bar
        self.progress_var.set(self.current_value)
        
        # Update percentage and count
        if self.maximum_value > 0:
            percentage = (self.current_value / self.maximum_value) * 100
            self.percentage_label.config(text=f"{percentage:.1f}%")
            self.count_label.config(text=f"({int(self.current_value)}/{int(self.maximum_value)})")
        else:
            self.percentage_label.config(text="0%")
            self.count_label.config(text="(0/0)")

        # Update elapsed time
        elapsed = datetime.now() - self.start_time
        elapsed_str = str(elapsed).split('.')[0]  # Remove microseconds
        self.elapsed_label.config(text=f"Elapsed: {elapsed_str}")

        # Calculate and update speed
        if elapsed.total_seconds() > 0 and self.current_value > 0:
            items_per_second = self.current_value / elapsed.total_seconds()
            if items_per_second >= 1:
                self.speed_label.config(text=f"{items_per_second:.1f} items/sec")
            else:
                self.speed_label.config(text=f"{items_per_second:.2f} items/sec")
        else:
            self.speed_label.config(text="")

        # Enhanced ETA calculation - try to extract from status message first
        eta_text = "ETA: Calculating..."
        if " | ETA: " in self.status_text:
            parts = self.status_text.split(" | ETA: ")
            if len(parts) > 1:
                eta_part = parts[1]
                if " | " in eta_part:
                    eta_text = f"ETA: {eta_part.split(' | ')[0]}"
                else:
                    eta_text = f"ETA: {eta_part}"
        elif self.current_value > 0 and self.maximum_value > 0:
            # Fallback to simple calculation
            progress_ratio = self.current_value / self.maximum_value
            if progress_ratio > 0:
                total_estimated = elapsed / progress_ratio
                remaining = total_estimated - elapsed
                if remaining.total_seconds() > 0:
                    remaining_str = str(remaining).split('.')[0]
                    eta_text = f"ETA: {remaining_str}"
                else:
                    eta_text = "ETA: Almost done"

        self.eta_label.config(text=eta_text)

        # Update status (clean up ETA info from display)
        display_status = self.status_text
        if " | ETA: " in display_status:
            display_status = display_status.split(" | ETA: ")[0]
        self.status_label.config(text=display_status)
    
    def _toggle_details(self):
        """Toggle details visibility."""
        if self.details_visible:
            self.details_frame.pack_forget()
            self.details_button.config(text="Show Details")
            self.dialog.geometry("500x200")
            self.details_visible = False
        else:
            self.details_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
            self.details_button.config(text="Hide Details")
            self.dialog.geometry("500x350")
            self.details_visible = True
    
    def _on_cancel(self):
        """Handle cancel button click."""
        if self.on_cancel:
            self.cancelled = True
            self.cancel_button.config(state=tk.DISABLED, text="Cancelling...")
            self.status_text = "Cancelling operation..."
            self.on_cancel()
        else:
            self.close()
    
    def _on_close(self):
        """Handle dialog close."""
        if self.can_cancel and not self.cancelled:
            self._on_cancel()
        else:
            self.close()
    
    # Public methods
    def update_progress(self, current: int, maximum: int = None, status: str = None):
        """Update progress values."""
        self.current_value = current
        if maximum is not None:
            self.maximum_value = maximum
        if status is not None:
            self.status_text = status
        
        logger.debug(f"Progress updated: {current}/{self.maximum_value} - {status}")
    
    def set_indeterminate(self, indeterminate: bool = True):
        """Set progress bar to indeterminate mode."""
        if indeterminate:
            self.progress_bar.config(mode='indeterminate')
            self.progress_bar.start()
        else:
            self.progress_bar.stop()
            self.progress_bar.config(mode='determinate')
    
    def add_detail(self, message: str):
        """Add a detail message to the details text."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        detail_line = f"[{timestamp}] {message}\n"
        
        self.details_text.config(state=tk.NORMAL)
        self.details_text.insert(tk.END, detail_line)
        self.details_text.see(tk.END)
        self.details_text.config(state=tk.DISABLED)
        
        logger.debug(f"Detail added: {message}")
    
    def set_complete(self, success: bool = True, message: str = None):
        """Mark the operation as complete."""
        if success:
            self.current_value = self.maximum_value
            self.status_text = message or "Operation completed successfully"
            if self.can_cancel:
                self.cancel_button.config(text="Close", state=tk.NORMAL)
        else:
            self.status_text = message or "Operation failed"
            if self.can_cancel:
                self.cancel_button.config(text="Close", state=tk.NORMAL)
        
        self.add_detail(self.status_text)
        logger.info(f"Operation complete: {self.status_text}")
    
    def close(self):
        """Close the dialog."""
        if self.dialog.winfo_exists():
            self.dialog.grab_release()
            self.dialog.destroy()
        logger.debug("ProgressDialog closed")
    
    def is_cancelled(self) -> bool:
        """Check if the operation was cancelled."""
        return self.cancelled
    
    def show(self):
        """Show the dialog (blocking)."""
        self.dialog.wait_window()


class ProgressManager:
    """Manager for handling multiple progress dialogs."""
    
    def __init__(self):
        self.active_dialogs: dict = {}
    
    def create_progress(self, parent, operation_id: str, title: str = "Progress",
                       can_cancel: bool = True, on_cancel: Callable = None) -> ProgressDialog:
        """Create a new progress dialog."""
        if operation_id in self.active_dialogs:
            # Close existing dialog for this operation
            self.active_dialogs[operation_id].close()
        
        dialog = ProgressDialog(parent, title, can_cancel, on_cancel)
        self.active_dialogs[operation_id] = dialog
        
        return dialog
    
    def get_progress(self, operation_id: str) -> Optional[ProgressDialog]:
        """Get an existing progress dialog."""
        return self.active_dialogs.get(operation_id)
    
    def close_progress(self, operation_id: str):
        """Close a specific progress dialog."""
        if operation_id in self.active_dialogs:
            self.active_dialogs[operation_id].close()
            del self.active_dialogs[operation_id]
    
    def close_all(self):
        """Close all active progress dialogs."""
        for dialog in list(self.active_dialogs.values()):
            dialog.close()
        self.active_dialogs.clear()


# Global progress manager instance
_progress_manager = ProgressManager()

def get_progress_manager() -> ProgressManager:
    """Get the global progress manager."""
    return _progress_manager


if __name__ == "__main__":
    # Test the ProgressDialog
    import threading
    
    root = tk.Tk()
    root.title("Progress Dialog Test")
    root.geometry("300x200")
    
    def test_operation():
        """Test operation with progress updates."""
        dialog = ProgressDialog(root, "Test Operation", can_cancel=True)
        
        def run_operation():
            for i in range(101):
                if dialog.is_cancelled():
                    break
                
                dialog.update_progress(i, 100, f"Processing item {i}/100")
                dialog.add_detail(f"Completed step {i}")
                time.sleep(0.05)  # Simulate work
            
            if not dialog.is_cancelled():
                dialog.set_complete(True, "Test operation completed!")
            else:
                dialog.set_complete(False, "Test operation cancelled")
        
        # Run operation in background thread
        thread = threading.Thread(target=run_operation)
        thread.daemon = True
        thread.start()
        
        dialog.show()
    
    ttk.Button(root, text="Start Test Operation", command=test_operation).pack(pady=50)
    
    root.mainloop()
