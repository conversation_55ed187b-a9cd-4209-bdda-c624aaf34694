#!/usr/bin/env python3
"""
Test script to validate the progress tracking and ETA fixes.
"""

import sys
import os
import time
import threading
from datetime import datetime

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_eta_estimator():
    """Test the ETA estimator functionality."""
    print("Testing ETA Estimator...")
    
    try:
        from src.utils.eta import get_eta_estimator
        
        estimator = get_eta_estimator()
        
        # Start a test task
        task_id = "test_task"
        estimator.start_task(task_id, "test", 100, complexity={'test': True})
        
        print(f"✓ Started task: {task_id}")
        
        # Simulate progress updates
        for i in range(1, 11):
            time.sleep(0.1)  # Simulate work
            eta_info = estimator.update_task_progress(task_id, i * 10, 0.1)
            
            print(f"  Progress: {i*10}/100 - ETA: {eta_info['eta_formatted']} - "
                  f"Speed: {eta_info['items_per_second']:.2f} items/sec - "
                  f"Confidence: {eta_info['confidence']:.2f}")
        
        # Complete the task
        estimator.complete_task(task_id)
        print("✓ Task completed successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ ETA Estimator test failed: {e}")
        return False

def test_progress_dialog():
    """Test the progress dialog functionality."""
    print("\nTesting Progress Dialog...")
    
    try:
        import tkinter as tk
        from src.gui.progress_dialog import ProgressDialog
        
        # Create a test window
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        
        # Create progress dialog
        dialog = ProgressDialog(root, "Test Progress", can_cancel=True)
        
        def test_progress():
            for i in range(101):
                if dialog.is_cancelled():
                    break
                
                dialog.update_progress(i, 100, f"Processing item {i}/100 | ETA: {(100-i)*0.1:.1f}s | 10.0 items/sec")
                dialog.add_detail(f"Completed step {i}")
                time.sleep(0.05)
            
            if not dialog.is_cancelled():
                dialog.set_complete(True, "Test completed successfully!")
            else:
                dialog.set_complete(False, "Test cancelled")
        
        # Run test in background
        thread = threading.Thread(target=test_progress, daemon=True)
        thread.start()
        
        # Show dialog for a short time
        root.after(3000, lambda: dialog.close())  # Auto-close after 3 seconds
        dialog.show()
        
        print("✓ Progress Dialog test completed")
        return True
        
    except Exception as e:
        print(f"✗ Progress Dialog test failed: {e}")
        return False

def test_enhanced_status():
    """Test the enhanced status widget."""
    print("\nTesting Enhanced Status Widget...")
    
    try:
        import tkinter as tk
        from src.gui.enhanced_status import EnhancedStatusWidget
        
        # Create a test window
        root = tk.Tk()
        root.title("Enhanced Status Test")
        root.geometry("600x300")
        
        # Create enhanced status widget
        status_widget = EnhancedStatusWidget(root)
        
        def test_status():
            # Start operation
            status_widget.start_operation("Test Analysis", 50)
            
            for i in range(1, 51):
                time.sleep(0.1)
                eta = f"{(50-i)*0.1:.1f}s"
                speed = 10.0
                current_task = f"Processing item {i}: Sample task description"
                
                status_widget.update_progress(i, 50, current_task, eta, speed)
                root.update()
            
            status_widget.complete_operation(True, "Test completed successfully!")
        
        # Run test in background
        thread = threading.Thread(target=test_status, daemon=True)
        thread.start()
        
        # Show window for a short time
        root.after(6000, root.quit)  # Auto-close after 6 seconds
        root.mainloop()
        
        print("✓ Enhanced Status Widget test completed")
        return True
        
    except Exception as e:
        print(f"✗ Enhanced Status Widget test failed: {e}")
        return False

def test_cli_progress():
    """Test CLI progress improvements."""
    print("\nTesting CLI Progress Improvements...")
    
    try:
        from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
        from rich.console import Console
        
        console = Console()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(bar_width=40),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TextColumn("•"),
            TextColumn("[cyan]{task.fields[status]}[/cyan]"),
            TimeElapsedColumn(),
            TextColumn("•"),
            TextColumn("[yellow]{task.fields[eta]}[/yellow]"),
            console=console,
            refresh_per_second=4
        ) as progress:
            
            task = progress.add_task(
                "Test Analysis", 
                total=50,
                status="Initializing...",
                eta="Calculating..."
            )
            
            for i in range(1, 51):
                time.sleep(0.1)
                eta_text = f"{(50-i)*0.1:.1f}s"
                status_text = f"Processing item {i}: Sample analysis task"
                
                progress.update(
                    task,
                    completed=i,
                    status=status_text[:60] + "..." if len(status_text) > 60 else status_text,
                    eta=eta_text
                )
            
            progress.update(task, completed=50, status="Complete!", eta="Done")
        
        print("✓ CLI Progress test completed")
        return True
        
    except Exception as e:
        print(f"✗ CLI Progress test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING PROGRESS TRACKING AND ETA FIXES")
    print("=" * 60)
    
    tests = [
        test_eta_estimator,
        test_cli_progress,
        test_progress_dialog,
        test_enhanced_status
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Progress tracking fixes are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
