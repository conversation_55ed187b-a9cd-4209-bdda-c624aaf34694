# Main GUI window for Deep Research Tool
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import json
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime

from ..models.hierarchy import Hierarchy
from ..models.result import AnalysisResult, AnalysisStatus
from ..analysis.recursive import RecursiveAnalyzer
from ..analysis.iterative import IterativeAnalyzer
from ..ollama.model_manager import get_model_manager
from ..config.settings import settings
from ..utils.logging_config import get_logger
from .hierarchy_explorer import HierarchyExplorer
from .progress_dialog import ProgressDialog
from .settings_dialog import SettingsDialog
from .enhanced_status import EnhancedStatusWidget

logger = get_logger("gui.main")


class MainGUI:
    """Main GUI application window."""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Deep Research Tool")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)

        # Application state
        self.hierarchy: Optional[Hierarchy] = None
        self.analysis_results: Dict[str, AnalysisResult] = {}
        self.current_model = settings.get("ollama", "default_model", "llama2")

        # GUI components
        self.hierarchy_explorer: Optional[HierarchyExplorer] = None
        self.progress_dialog: Optional[ProgressDialog] = None
        self.enhanced_status: Optional[EnhancedStatusWidget] = None

        # Threading
        self.analysis_thread: Optional[threading.Thread] = None
        self.result_queue = queue.Queue()

        # Model manager
        self.model_manager = get_model_manager()

        # Setup GUI
        self._setup_menu()
        self._setup_toolbar()
        self._setup_main_layout()
        self._setup_status_bar()

        # Start periodic updates
        self._schedule_updates()

        logger.info("Main GUI initialized")

    def _setup_menu(self):
        """Setup the main menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Hierarchy", command=self._new_hierarchy)
        file_menu.add_command(label="Open CSV...", command=self._open_csv)
        file_menu.add_command(label="Open JSON...", command=self._open_json)
        file_menu.add_separator()
        file_menu.add_command(label="Save Hierarchy...", command=self._save_hierarchy)
        file_menu.add_command(label="Export Results...", command=self._export_results)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self._on_closing)

        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Add Topic...", command=self._add_topic)
        edit_menu.add_command(label="Edit Topic...", command=self._edit_topic)
        edit_menu.add_command(label="Delete Topic", command=self._delete_topic)
        edit_menu.add_separator()
        edit_menu.add_command(label="Preferences...", command=self._show_preferences)

        # Analysis menu
        analysis_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Analysis", menu=analysis_menu)
        analysis_menu.add_command(label="Recursive Analysis", command=self._run_recursive_analysis)
        analysis_menu.add_command(label="Iterative Analysis", command=self._run_iterative_analysis)
        analysis_menu.add_separator()
        analysis_menu.add_command(label="Stop Analysis", command=self._stop_analysis)
        analysis_menu.add_command(label="Clear Results", command=self._clear_results)

        # Models menu
        models_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Models", menu=models_menu)
        models_menu.add_command(label="Refresh Models", command=self._refresh_models)
        models_menu.add_command(label="Select Model...", command=self._select_model)
        models_menu.add_command(label="Test Model", command=self._test_model)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self._show_about)
        help_menu.add_command(label="User Guide", command=self._show_help)

    def _setup_toolbar(self):
        """Setup the toolbar."""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)

        # File operations
        ttk.Button(toolbar, text="New", command=self._new_hierarchy).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Open", command=self._open_csv).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Save", command=self._save_hierarchy).pack(side=tk.LEFT, padx=2)

        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # Analysis operations
        ttk.Button(toolbar, text="Recursive", command=self._run_recursive_analysis).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Iterative", command=self._run_iterative_analysis).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Stop", command=self._stop_analysis).pack(side=tk.LEFT, padx=2)

        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # Model selection
        ttk.Label(toolbar, text="Model:").pack(side=tk.LEFT, padx=2)
        self.model_var = tk.StringVar(value=self.current_model)
        self.model_combo = ttk.Combobox(toolbar, textvariable=self.model_var, width=15, state="readonly")
        self.model_combo.pack(side=tk.LEFT, padx=2)
        self.model_combo.bind("<<ComboboxSelected>>", self._on_model_selected)

        # Refresh models
        ttk.Button(toolbar, text="Refresh", command=self._refresh_models).pack(side=tk.LEFT, padx=2)

    def _setup_main_layout(self):
        """Setup the main layout with paned windows."""
        # Main paned window (horizontal split)
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Hierarchy Explorer
        left_frame = ttk.LabelFrame(main_paned, text="Hierarchy", padding=5)
        main_paned.add(left_frame, weight=1)

        self.hierarchy_explorer = HierarchyExplorer(left_frame, self._on_node_selected)

        # Right panel - Results and Details
        right_paned = ttk.PanedWindow(main_paned, orient=tk.VERTICAL)
        main_paned.add(right_paned, weight=2)

        # Results panel
        results_frame = ttk.LabelFrame(right_paned, text="Analysis Results", padding=5)
        right_paned.add(results_frame, weight=1)

        self._setup_results_panel(results_frame)

        # Details panel
        details_frame = ttk.LabelFrame(right_paned, text="Details", padding=5)
        right_paned.add(details_frame, weight=1)

        self._setup_details_panel(details_frame)

        # Enhanced status panel
        self.enhanced_status = EnhancedStatusWidget(self.root)

    def _setup_results_panel(self, parent):
        """Setup the results panel."""
        # Results tree
        columns = ("Node", "Status", "Quality", "Length")
        self.results_tree = ttk.Treeview(parent, columns=columns, show="tree headings", height=10)

        # Configure columns
        self.results_tree.heading("#0", text="Topic")
        self.results_tree.heading("Node", text="Node ID")
        self.results_tree.heading("Status", text="Status")
        self.results_tree.heading("Quality", text="Quality")
        self.results_tree.heading("Length", text="Length")

        self.results_tree.column("#0", width=200)
        self.results_tree.column("Node", width=100)
        self.results_tree.column("Status", width=80)
        self.results_tree.column("Quality", width=60)
        self.results_tree.column("Length", width=60)

        # Scrollbars for results tree
        results_scroll_y = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.results_tree.yview)
        results_scroll_x = ttk.Scrollbar(parent, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=results_scroll_y.set, xscrollcommand=results_scroll_x.set)

        # Pack results tree and scrollbars
        self.results_tree.grid(row=0, column=0, sticky="nsew")
        results_scroll_y.grid(row=0, column=1, sticky="ns")
        results_scroll_x.grid(row=1, column=0, sticky="ew")

        parent.grid_rowconfigure(0, weight=1)
        parent.grid_columnconfigure(0, weight=1)

        # Bind selection event
        self.results_tree.bind("<<TreeviewSelect>>", self._on_result_selected)

    def _setup_details_panel(self, parent):
        """Setup the details panel."""
        # Create notebook for different detail views
        self.details_notebook = ttk.Notebook(parent)
        self.details_notebook.pack(fill=tk.BOTH, expand=True)

        # Analysis content tab
        content_frame = ttk.Frame(self.details_notebook)
        self.details_notebook.add(content_frame, text="Analysis Content")

        self.content_text = scrolledtext.ScrolledText(content_frame, wrap=tk.WORD, height=15)
        self.content_text.pack(fill=tk.BOTH, expand=True)

        # Metadata tab
        metadata_frame = ttk.Frame(self.details_notebook)
        self.details_notebook.add(metadata_frame, text="Metadata")

        self.metadata_text = scrolledtext.ScrolledText(metadata_frame, wrap=tk.WORD, height=15)
        self.metadata_text.pack(fill=tk.BOTH, expand=True)

        # Quality metrics tab
        quality_frame = ttk.Frame(self.details_notebook)
        self.details_notebook.add(quality_frame, text="Quality Metrics")

        self.quality_text = scrolledtext.ScrolledText(quality_frame, wrap=tk.WORD, height=15)
        self.quality_text.pack(fill=tk.BOTH, expand=True)

    def _setup_status_bar(self):
        """Setup the status bar."""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Status label
        self.status_label = ttk.Label(self.status_bar, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5)

        # Output directory info (center)
        output_info_frame = ttk.Frame(self.status_bar)
        output_info_frame.pack(side=tk.LEFT, expand=True, padx=(20, 0))

        ttk.Label(output_info_frame, text="Output:").pack(side=tk.LEFT)

        self.output_dir_label = ttk.Label(output_info_frame,
                                         text=settings.get("paths", "output_dir", "./output_results"),
                                         foreground="blue",
                                         cursor="hand2")
        self.output_dir_label.pack(side=tk.LEFT, padx=(5, 0))
        self.output_dir_label.bind("<Button-1>", self._open_output_directory)

        ttk.Button(output_info_frame, text="📁", width=3,
                  command=self._change_output_directory).pack(side=tk.LEFT, padx=(5, 0))

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.status_bar,
            variable=self.progress_var,
            maximum=100,
            length=200
        )
        self.progress_bar.pack(side=tk.RIGHT, padx=5)

        # Model status
        self.model_status_label = ttk.Label(self.status_bar, text=f"Model: {self.current_model}")
        self.model_status_label.pack(side=tk.RIGHT, padx=10)

    def _schedule_updates(self):
        """Schedule periodic GUI updates."""
        self._process_result_queue()
        self.root.after(100, self._schedule_updates)  # Update every 100ms

    def _process_result_queue(self):
        """Process results from background threads."""
        try:
            while True:
                message = self.result_queue.get_nowait()
                self._handle_background_message(message)
        except queue.Empty:
            pass

    def _handle_background_message(self, message):
        """Handle messages from background threads."""
        msg_type = message.get('type')

        if msg_type == 'progress':
            self._update_progress(message['current'], message['total'], message['status'])
        elif msg_type == 'result':
            self._add_analysis_result(message['node_id'], message['result'])
        elif msg_type == 'complete':
            self._analysis_complete(message['summary'])
        elif msg_type == 'error':
            self._analysis_error(message['error'])
        elif msg_type == 'model_test_success':
            self._model_test_success(message['response'])
        elif msg_type == 'model_test_error':
            self._model_test_error(message['error'])

    # File operations
    def _new_hierarchy(self):
        """Create a new hierarchy."""
        # Check if we need to discard current hierarchy
        if self.hierarchy:
            if not messagebox.askyesno("New Hierarchy", "Discard current hierarchy and create a new one?"):
                return

        # Get hierarchy name from user
        from tkinter import simpledialog
        hierarchy_name = simpledialog.askstring(
            "New Hierarchy",
            "Enter name for the new hierarchy:",
            initialvalue="New Hierarchy"
        )

        if not hierarchy_name:
            return  # User cancelled

        # Get hierarchy description
        hierarchy_description = simpledialog.askstring(
            "New Hierarchy",
            "Enter description (optional):",
            initialvalue="Created from GUI"
        )

        # Create new hierarchy
        self.hierarchy = Hierarchy(hierarchy_name, hierarchy_description or "Created from GUI")
        self.analysis_results.clear()
        self.hierarchy_explorer.load_hierarchy(self.hierarchy)
        self._clear_results_display()
        self._update_status(f"New hierarchy '{hierarchy_name}' created")

        # Ask if user wants to add topics immediately
        if messagebox.askyesno("Add Topics", "Would you like to add topics to the new hierarchy now?"):
            self._add_topic()

    def _open_csv(self):
        """Open hierarchy from CSV file."""
        filename = filedialog.askopenfilename(
            title="Open CSV File",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if filename:
            try:
                self.hierarchy = Hierarchy("Imported Hierarchy", f"Loaded from {filename}")
                topics = self.hierarchy.import_from_csv(filename, has_header=True)

                self.hierarchy_explorer.load_hierarchy(self.hierarchy)
                self._update_status(f"Loaded {len(topics)} topics from CSV")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to load CSV: {str(e)}")
                logger.error(f"CSV load error: {str(e)}", exc_info=True)

    def _open_json(self):
        """Open hierarchy from JSON file."""
        filename = filedialog.askopenfilename(
            title="Open JSON File",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r') as f:
                    data = json.load(f)

                if 'hierarchy' in data:
                    self.hierarchy = Hierarchy.from_dict(data['hierarchy'])
                else:
                    self.hierarchy = Hierarchy.from_dict(data)

                self.hierarchy_explorer.load_hierarchy(self.hierarchy)
                self._update_status(f"Loaded hierarchy from JSON")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to load JSON: {str(e)}")
                logger.error(f"JSON load error: {str(e)}", exc_info=True)

    def _save_hierarchy(self):
        """Save hierarchy to file."""
        if not self.hierarchy:
            messagebox.showwarning("Warning", "No hierarchy to save")
            return

        filename = filedialog.asksaveasfilename(
            title="Save Hierarchy",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if filename:
            try:
                if filename.endswith('.csv'):
                    self.hierarchy.export_to_csv(filename)
                else:
                    with open(filename, 'w') as f:
                        json.dump(self.hierarchy.to_dict(), f, indent=2)

                self._update_status(f"Hierarchy saved to {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to save: {str(e)}")
                logger.error(f"Save error: {str(e)}", exc_info=True)

    def _export_results(self):
        """Export analysis results."""
        if not self.analysis_results:
            messagebox.showwarning("Warning", "No results to export")
            return

        # Get default output directory
        default_output_dir = settings.get("paths", "output_dir", "./output_results")

        # Ensure output directory exists
        Path(default_output_dir).mkdir(parents=True, exist_ok=True)

        filename = filedialog.asksaveasfilename(
            title="Export Results",
            initialdir=default_output_dir,
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("CSV files", "*.csv"), ("Text files", "*.txt")]
        )

        if filename:
            try:
                # Ensure the directory exists
                Path(filename).parent.mkdir(parents=True, exist_ok=True)

                if filename.endswith('.json'):
                    export_data = {
                        'export_time': datetime.now().isoformat(),
                        'hierarchy': self.hierarchy.to_dict() if self.hierarchy else None,
                        'results': {node_id: result.to_dict() for node_id, result in self.analysis_results.items()}
                    }
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(export_data, f, indent=2, ensure_ascii=False)

                elif filename.endswith('.csv'):
                    # Export as CSV
                    import csv
                    with open(filename, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        writer.writerow(['Node ID', 'Status', 'Quality Score', 'Content Length', 'Content'])

                        for node_id, result in self.analysis_results.items():
                            quality_score = result.calculate_quality_score() if hasattr(result, 'calculate_quality_score') else 0
                            writer.writerow([
                                node_id,
                                result.status.value,
                                f"{quality_score:.2f}",
                                len(result.content),
                                result.content.replace('\n', ' ').replace('\r', '')
                            ])

                elif filename.endswith('.txt'):
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(f"Analysis Results Export\n")
                        f.write(f"Generated: {datetime.now().isoformat()}\n")
                        f.write(f"Total Results: {len(self.analysis_results)}\n")
                        f.write("=" * 50 + "\n\n")

                        for node_id, result in self.analysis_results.items():
                            f.write(f"Node ID: {node_id}\n")
                            f.write(f"Status: {result.status.value}\n")
                            quality_score = result.calculate_quality_score() if hasattr(result, 'calculate_quality_score') else 0
                            f.write(f"Quality Score: {quality_score:.2f}\n")
                            f.write(f"Content Length: {len(result.content)}\n")
                            f.write(f"Content:\n{result.content}\n")
                            f.write("-" * 30 + "\n\n")

                self._update_status(f"Results exported to {filename}")

                # Show success message with option to open folder
                if messagebox.askyesno("Export Complete",
                                     f"Results exported successfully to:\n{filename}\n\nWould you like to open the containing folder?"):
                    import subprocess
                    import platform

                    folder_path = Path(filename).parent
                    if platform.system() == "Windows":
                        subprocess.run(["explorer", str(folder_path)])
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", str(folder_path)])
                    else:  # Linux
                        subprocess.run(["xdg-open", str(folder_path)])

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export: {str(e)}")
                logger.error(f"Export error: {str(e)}", exc_info=True)

    def _on_closing(self):
        """Handle window closing with proper cleanup."""
        # Check for running analysis
        if self.analysis_thread and self.analysis_thread.is_alive():
            if messagebox.askyesno("Exit", "Analysis is running. Stop and exit?"):
                self._stop_analysis()
                self.root.after(1000, self._perform_cleanup_and_exit)  # Give time for cleanup
            return

        # Check for unsaved changes
        if self.hierarchy and not self._check_unsaved_changes():
            return

        self._perform_cleanup_and_exit()

    def _check_unsaved_changes(self):
        """Check if there are unsaved changes and ask user."""
        # For now, just ask if user wants to save
        if self.hierarchy:
            response = messagebox.askyesnocancel(
                "Unsaved Changes",
                "Do you want to save your hierarchy before exiting?\n\n"
                "Yes = Save and exit\n"
                "No = Exit without saving\n"
                "Cancel = Don't exit"
            )
            if response is None:  # Cancel
                return False
            elif response:  # Yes - save
                self._save_hierarchy()
        return True

    def _perform_cleanup_and_exit(self):
        """Perform cleanup and exit the application."""
        try:
            self._update_status("Shutting down...")

            # Stop any background threads
            if self.analysis_thread and self.analysis_thread.is_alive():
                # Note: In a full implementation, you'd implement proper thread cancellation
                pass

            # Clear large data structures
            if self.analysis_results:
                self.analysis_results.clear()

            # Close model connections
            try:
                if hasattr(self.model_manager, 'cleanup'):
                    self.model_manager.cleanup()
            except:
                pass

            # Flush logs
            try:
                import logging
                logging.shutdown()
            except:
                pass

            logger.info("GUI application shutting down")

        except Exception as e:
            logger.error(f"Cleanup error: {str(e)}", exc_info=True)
        finally:
            self.root.destroy()

    def _open_output_directory(self, event=None):
        """Open the output directory in file explorer."""
        try:
            import subprocess
            import platform

            output_dir = settings.get("paths", "output_dir", "./output_results")
            folder_path = Path(output_dir)

            # Ensure directory exists
            folder_path.mkdir(parents=True, exist_ok=True)

            if platform.system() == "Windows":
                subprocess.run(["explorer", str(folder_path)])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(folder_path)])
            else:  # Linux
                subprocess.run(["xdg-open", str(folder_path)])

        except Exception as e:
            messagebox.showerror("Error", f"Failed to open output directory: {str(e)}")
            logger.error(f"Failed to open output directory: {str(e)}", exc_info=True)

    def _change_output_directory(self):
        """Change the output directory."""
        current_dir = settings.get("paths", "output_dir", "./output_results")

        new_dir = filedialog.askdirectory(
            title="Select Output Directory",
            initialdir=current_dir
        )

        if new_dir:
            # Update settings
            settings.set("paths", "output_dir", new_dir)

            # Update the label
            self.output_dir_label.config(text=new_dir)

            # Update status
            self._update_status(f"Output directory changed to: {new_dir}")

            # Ensure directory exists
            Path(new_dir).mkdir(parents=True, exist_ok=True)

    # Event handlers
    def _on_node_selected(self, node_id):
        """Handle node selection in hierarchy explorer."""
        if node_id in self.analysis_results:
            result = self.analysis_results[node_id]
            self._display_result_details(result)

    def _on_result_selected(self, event):
        """Handle result selection in results tree."""
        selection = self.results_tree.selection()
        if selection:
            item = selection[0]
            node_id = self.results_tree.item(item, "values")[0]  # Node ID is first column
            if node_id in self.analysis_results:
                result = self.analysis_results[node_id]
                self._display_result_details(result)

    def _on_model_selected(self, event):
        """Handle model selection."""
        self.current_model = self.model_var.get()
        self.model_status_label.config(text=f"Model: {self.current_model}")
        self._update_status(f"Selected model: {self.current_model}")

    # Placeholder methods for remaining functionality
    def _add_topic(self):
        """Add a new topic to the hierarchy."""
        if not self.hierarchy:
            messagebox.showwarning("Warning", "No hierarchy loaded. Please create a new hierarchy first.")
            return

        from tkinter import simpledialog

        # Show instructions
        messagebox.showinfo(
            "Add Topic",
            "Enter topics using comma-separated format:\n\n"
            "1. Enter main topic name\n"
            "2. Enter sub-angles separated by commas\n\n"
            "Example:\n"
            "Topic: Technology\n"
            "Sub-angles: AI, Machine Learning, Robotics, IoT\n\n"
            "This creates a topic with multiple analysis angles."
        )

        # Get topic name
        topic_name = simpledialog.askstring(
            "Add Topic",
            "Enter the main topic name:",
            initialvalue=""
        )
        if not topic_name or not topic_name.strip():
            return

        topic_name = topic_name.strip()

        # Get sub-angles
        sub_angles_input = simpledialog.askstring(
            "Add Sub-angles",
            f"Enter sub-angles for '{topic_name}' (comma-separated):\n\n"
            f"Example: AI, Machine Learning, Robotics\n"
            f"Or leave empty for topic without sub-angles"
        )

        # Create layers
        layers = [topic_name]

        if sub_angles_input and sub_angles_input.strip():
            sub_angles = [angle.strip() for angle in sub_angles_input.split(',') if angle.strip()]
            if sub_angles:
                layers.extend(sub_angles[:4])  # Limit to 4 sub-angles
                if len(sub_angles) > 4:
                    messagebox.showwarning(
                        "Too Many Sub-angles",
                        f"Only the first 4 sub-angles will be used.\n"
                        f"Using: {', '.join(sub_angles[:4])}"
                    )

        try:
            topic = self.hierarchy.create_topic_from_layers(layers, topic_name)
            self.hierarchy_explorer.refresh()

            sub_count = len(layers) - 1
            status_msg = f"Added topic '{topic_name}'"
            if sub_count > 0:
                status_msg += f" with {sub_count} sub-angles"
            self._update_status(status_msg)

            # Ask if user wants to add another topic
            if messagebox.askyesno("Add Another Topic", "Topic created successfully! Would you like to add another topic?"):
                self._add_topic()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to add topic: {str(e)}")
            logger.error(f"Add topic error: {str(e)}", exc_info=True)

    def _edit_topic(self):
        """Edit selected topic."""
        selected_node_id = self.hierarchy_explorer.get_selected_node_id()
        if not selected_node_id:
            messagebox.showwarning("Warning", "No node selected")
            return

        messagebox.showinfo("Info", "Topic editing not yet fully implemented")

    def _delete_topic(self):
        """Delete selected topic."""
        selected_node_id = self.hierarchy_explorer.get_selected_node_id()
        if not selected_node_id:
            messagebox.showwarning("Warning", "No node selected")
            return

        if messagebox.askyesno("Confirm Delete", "Delete selected topic and all its data?"):
            messagebox.showinfo("Info", "Topic deletion not yet fully implemented")

    def _show_preferences(self):
        """Show preferences dialog."""
        try:
            dialog = SettingsDialog(self.root)
            dialog.show()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open preferences: {str(e)}")
            logger.error(f"Preferences error: {str(e)}", exc_info=True)

    def _run_recursive_analysis(self):
        """Run recursive analysis in background thread."""
        if not self.hierarchy:
            messagebox.showwarning("Warning", "No hierarchy loaded")
            return

        if self.analysis_thread and self.analysis_thread.is_alive():
            messagebox.showwarning("Warning", "Analysis already running")
            return

        # Get system prompt from user
        system_prompt = self._get_system_prompt()
        if system_prompt is None:
            return  # User cancelled

        # Create progress dialog
        self.progress_dialog = ProgressDialog(
            self.root,
            "Recursive Analysis",
            can_cancel=True,
            on_cancel=self._stop_analysis
        )

        # Calculate total nodes for accurate progress
        total_nodes = sum(topic.get_node_count() for topic in self.hierarchy.topics.values())
        self.progress_dialog.update_progress(0, total_nodes, "Initializing analysis...")

        # Start enhanced status tracking
        if self.enhanced_status:
            self.enhanced_status.start_operation("Recursive Analysis", total_nodes)

        # Start analysis in background thread
        self.analysis_thread = threading.Thread(
            target=self._run_analysis_background,
            args=("recursive", system_prompt),
            daemon=True
        )
        self.analysis_thread.start()

        # Show progress dialog
        self.progress_dialog.show()
        self._update_status("Starting recursive analysis...")

    def _run_iterative_analysis(self):
        """Run iterative analysis in background thread."""
        if not self.hierarchy:
            messagebox.showwarning("Warning", "No hierarchy loaded")
            return

        if self.analysis_thread and self.analysis_thread.is_alive():
            messagebox.showwarning("Warning", "Analysis already running")
            return

        # Get system prompt from user
        system_prompt = self._get_system_prompt()
        if system_prompt is None:
            return  # User cancelled

        # Create progress dialog
        self.progress_dialog = ProgressDialog(
            self.root,
            "Iterative Analysis",
            can_cancel=True,
            on_cancel=self._stop_analysis
        )

        # Calculate estimated total work for iterative analysis
        total_nodes = sum(topic.get_node_count() for topic in self.hierarchy.topics.values())
        estimated_total_work = total_nodes * 3  # Default max_iterations
        self.progress_dialog.update_progress(0, estimated_total_work, "Initializing analysis...")

        # Start enhanced status tracking
        if self.enhanced_status:
            self.enhanced_status.start_operation("Iterative Analysis", estimated_total_work)

        # Start analysis in background thread
        self.analysis_thread = threading.Thread(
            target=self._run_analysis_background,
            args=("iterative", system_prompt),
            daemon=True
        )
        self.analysis_thread.start()

        # Show progress dialog
        self.progress_dialog.show()
        self._update_status("Starting iterative analysis...")

    def _get_system_prompt(self) -> Optional[str]:
        """Get system prompt from user."""
        from tkinter import simpledialog

        prompt = simpledialog.askstring(
            "System Prompt",
            "Enter system prompt for analysis:",
            initialvalue="Provide a comprehensive analysis of the given topic."
        )
        return prompt

    def _run_analysis_background(self, analysis_type: str, system_prompt: str):
        """Run analysis in background thread."""
        try:
            # Create analyzer
            if analysis_type == "recursive":
                analyzer = RecursiveAnalyzer(model_name=self.current_model)
            else:
                analyzer = IterativeAnalyzer(model_name=self.current_model, max_iterations=3)

            # Set progress callback
            def progress_callback(current, total, message):
                self.result_queue.put({
                    'type': 'progress',
                    'current': current,
                    'total': total,
                    'status': message
                })

            analyzer.set_progress_callback(progress_callback)

            # Run analysis
            results = analyzer.analyze_hierarchy(self.hierarchy, system_prompt=system_prompt)

            # Send results back to main thread
            for node_id, result in results.items():
                self.result_queue.put({
                    'type': 'result',
                    'node_id': node_id,
                    'result': result
                })

            # Send completion message
            summary = analyzer.get_analysis_summary()
            self.result_queue.put({
                'type': 'complete',
                'summary': summary
            })

        except Exception as e:
            logger.error(f"Analysis error: {str(e)}", exc_info=True)
            self.result_queue.put({
                'type': 'error',
                'error': str(e)
            })

    def _stop_analysis(self):
        """Stop running analysis."""
        if self.analysis_thread and self.analysis_thread.is_alive():
            # Close progress dialog
            if self.progress_dialog:
                self.progress_dialog.set_complete(False, "Analysis cancelled by user")
                self.progress_dialog = None

            # Cancel enhanced status
            if self.enhanced_status:
                self.enhanced_status.cancel_operation("Analysis cancelled by user")

            # Note: This is a simplified stop - in a real implementation,
            # you'd need to implement proper cancellation in the analyzers
            messagebox.showinfo("Info", "Analysis stopping... (may take a moment)")
            self._update_status("Stopping analysis...")
        else:
            # Close progress dialog if it exists
            if self.progress_dialog:
                self.progress_dialog.close()
                self.progress_dialog = None

            # Reset enhanced status
            if self.enhanced_status:
                self.enhanced_status.reset()

            messagebox.showinfo("Info", "No analysis running")

    def _clear_results(self):
        self.analysis_results.clear()
        self._clear_results_display()
        self._update_status("Results cleared")

    def _refresh_models(self):
        try:
            models = self.model_manager.get_available_models()
            model_names = [model.name for model in models]
            self.model_combo['values'] = model_names
            if model_names and self.current_model not in model_names:
                self.current_model = model_names[0]
                self.model_var.set(self.current_model)
            self._update_status(f"Found {len(models)} models")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh models: {str(e)}")

    def _select_model(self):
        """Show model selection dialog."""
        try:
            models = self.model_manager.get_available_models()
            if not models:
                messagebox.showwarning("Warning", "No models available")
                return

            # Create selection dialog
            from tkinter import simpledialog

            model_names = [model.name for model in models]
            model_info = "\n".join([f"{i+1}. {model.name}" for i, model in enumerate(models)])

            selection = simpledialog.askstring(
                "Select Model",
                f"Available models:\n{model_info}\n\nEnter model name:"
            )

            if selection and selection in model_names:
                # Validate model
                validation = self.model_manager.validate_model(selection)
                if validation['valid']:
                    self.current_model = selection
                    self.model_var.set(selection)
                    self.model_status_label.config(text=f"Model: {selection}")
                    self._update_status(f"Selected model: {selection}")
                else:
                    error_msg = "\n".join(validation['errors'])
                    messagebox.showerror("Model Error", f"Model validation failed:\n{error_msg}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to select model: {str(e)}")
            logger.error(f"Model selection error: {str(e)}", exc_info=True)

    def _test_model(self):
        """Test current model connection."""
        try:
            from ..ollama.api import get_ollama_client

            client = get_ollama_client()

            # Test with simple prompt
            test_prompt = "Hello, this is a test. Please respond with 'Test successful'."

            self._update_status("Testing model connection...")

            # Run test in background thread
            def test_model_background():
                try:
                    response = client.generate_simple(
                        model=self.current_model,
                        prompt=test_prompt,
                        options={'num_predict': 20}
                    )

                    # Send result back to main thread
                    self.result_queue.put({
                        'type': 'model_test_success',
                        'response': response
                    })

                except Exception as e:
                    self.result_queue.put({
                        'type': 'model_test_error',
                        'error': str(e)
                    })

            thread = threading.Thread(target=test_model_background, daemon=True)
            thread.start()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to test model: {str(e)}")
            logger.error(f"Model test error: {str(e)}", exc_info=True)

    def _show_about(self):
        messagebox.showinfo("About", "Deep Research Tool v1.0\nHierarchical Analysis with Ollama")

    def _show_help(self):
        messagebox.showinfo("Help", "User guide not yet implemented")

    # Utility methods
    def _update_status(self, message):
        """Update status bar message."""
        self.status_label.config(text=message)
        logger.info(f"Status: {message}")

    def _update_progress(self, current, total, status):
        """Update progress bar, progress dialog, and enhanced status."""
        # Update status bar progress
        if total > 0:
            progress = (current / total) * 100
            self.progress_var.set(progress)
        self._update_status(status)

        # Update progress dialog if it exists
        if self.progress_dialog and not self.progress_dialog.is_cancelled():
            self.progress_dialog.update_progress(current, total, status)
            # Add detail to progress dialog
            if status:
                self.progress_dialog.add_detail(status)

        # Update enhanced status widget
        if self.enhanced_status:
            # Extract ETA and speed from status message
            eta_text = ""
            speed = 0.0
            clean_status = status

            if " | ETA: " in status:
                parts = status.split(" | ETA: ")
                clean_status = parts[0]
                eta_part = parts[1]
                if " | " in eta_part:
                    eta_text = eta_part.split(" | ")[0]
                    speed_part = eta_part.split(" | ")[1]
                    if "nodes/sec" in speed_part:
                        try:
                            speed = float(speed_part.split()[0])
                        except (ValueError, IndexError):
                            pass
                else:
                    eta_text = eta_part

            self.enhanced_status.update_progress(
                current, total, clean_status, eta_text, speed
            )

    def _clear_results_display(self):
        """Clear the results display."""
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        self.content_text.delete(1.0, tk.END)
        self.metadata_text.delete(1.0, tk.END)
        self.quality_text.delete(1.0, tk.END)

    def _add_analysis_result(self, node_id, result):
        """Add analysis result to display."""
        self.analysis_results[node_id] = result

        # Add to results tree
        status_color = "green" if result.status == AnalysisStatus.COMPLETED else "red"
        quality_score = result.calculate_quality_score()

        self.results_tree.insert("", "end", values=(
            node_id[:8] + "...",
            result.status.value,
            f"{quality_score:.2f}" if quality_score > 0 else "N/A",
            str(len(result.content))
        ))

    def _display_result_details(self, result):
        """Display result details in the details panel."""
        # Content tab
        self.content_text.delete(1.0, tk.END)
        self.content_text.insert(1.0, result.content)

        # Metadata tab
        self.metadata_text.delete(1.0, tk.END)
        metadata_info = f"""Node ID: {result.node_id}
Topic ID: {result.topic_id}
Status: {result.status.value}
Created: {result.created_at}
Model: {result.metadata.model_name}
Duration: {result.metadata.get_duration_seconds():.2f}s
"""
        self.metadata_text.insert(1.0, metadata_info)

        # Quality metrics tab
        self.quality_text.delete(1.0, tk.END)
        if result.quality_metrics:
            quality_info = "Quality Metrics:\n\n"
            for metric, value in result.quality_metrics.items():
                quality_info += f"{metric.title()}: {value:.3f}\n"
            quality_info += f"\nOverall Score: {result.calculate_quality_score():.3f}"
        else:
            quality_info = "No quality metrics available"

        self.quality_text.insert(1.0, quality_info)

    def _analysis_complete(self, summary):
        """Handle analysis completion."""
        self._update_status("Analysis completed")
        self.progress_var.set(100)

        # Complete progress dialog
        if self.progress_dialog:
            self.progress_dialog.set_complete(True, f"Analysis completed with {summary.get('total_results', 0)} results")
            self.progress_dialog = None

        # Complete enhanced status
        if self.enhanced_status:
            self.enhanced_status.complete_operation(True, f"Completed with {summary.get('total_results', 0)} results")

        messagebox.showinfo("Analysis Complete", f"Analysis finished with {summary.get('total_results', 0)} results")

    def _analysis_error(self, error):
        """Handle analysis error."""
        self._update_status("Analysis failed")
        self.progress_var.set(0)

        # Complete progress dialog with error
        if self.progress_dialog:
            self.progress_dialog.set_complete(False, f"Analysis failed: {str(error)}")
            self.progress_dialog = None

        # Complete enhanced status with error
        if self.enhanced_status:
            self.enhanced_status.complete_operation(False, f"Failed: {str(error)}")

        messagebox.showerror("Analysis Error", f"Analysis failed: {str(error)}")

    def _model_test_success(self, response):
        """Handle successful model test."""
        self._update_status("Model test completed")
        messagebox.showinfo("Model Test", f"Model test successful!\n\nResponse: {response[:100]}...")

    def _model_test_error(self, error):
        """Handle model test error."""
        self._update_status("Model test failed")
        messagebox.showerror("Model Test", f"Model test failed: {str(error)}")

    def run(self):
        """Run the GUI application."""
        # Initialize with model refresh
        self._refresh_models()

        # Setup window closing handler
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        # Start the main loop
        self.root.mainloop()


def gui_main():
    """Main entry point for GUI mode."""
    try:
        app = MainGUI()
        app.run()
    except Exception as e:
        logger.error(f"GUI startup error: {str(e)}", exc_info=True)
        messagebox.showerror("Startup Error", f"Failed to start GUI: {str(e)}")


if __name__ == "__main__":
    gui_main()

# Define constants for backward compatibility
DEFAULT_INPUT_FILE = settings.get('paths', 'default_input_file', './input_topics_subangles_example.csv')
DEFAULT_OUTPUT_DIR = settings.get('paths', 'output_dir', './output_results')

# Example stub for GUI input selection
def select_input_method():
    # In a real GUI, this would be a dialog or radio button
    print("[GUI] Select input method: Manual or File")
    # ...existing code...

def get_topics_from_file(input_file=DEFAULT_INPUT_FILE):
    import csv
    topics = []
    with open(input_file, newline='', encoding='utf-8') as csvfile:
        reader = csv.reader(csvfile)
        for row in reader:
            if row and not row[0].startswith('#'):
                topics.append(row)
    return topics

# In the GUI, show the default output folder somewhere
def show_default_output_folder():
    print(f"[GUI] Default output folder: {DEFAULT_OUTPUT_DIR}")
    # ...existing code...
