#!/usr/bin/env python3
"""
Test script to verify export functionality works correctly.
This script tests both GUI and CLI export features.
"""

import os
import sys
import json
import csv
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config.settings import settings
from src.models.hierarchy import Hierarchy
from src.models.result import AnalysisResult, AnalysisStatus, AnalysisMetadata
from src.utils.file_io import write_json, read_json


def create_test_results():
    """Create test analysis results for testing export functionality."""
    results = {}

    # Create test hierarchy
    hierarchy = Hierarchy("Test Hierarchy", "Test hierarchy for export testing")

    # Create topics using the proper API
    topic1 = hierarchy.create_topic_from_layers(["Test Topic 1", "Subtopic 1"], "Test Topic 1")
    topic2 = hierarchy.create_topic_from_layers(["Test Topic 2", "Subtopic 2"], "Test Topic 2")

    # Get node IDs from the topics
    node1_id = list(topic1.nodes.keys())[0]  # Get first node ID
    node2_id = list(topic2.nodes.keys())[0]  # Get first node ID
    
    # Create test results
    result1 = AnalysisResult(topic_id="topic_1", node_id=node1_id)
    result1.set_content("This is test analysis content for topic 1. It contains detailed analysis and insights.")
    result1.set_status(AnalysisStatus.COMPLETED)
    result1.metadata.model_name = "test_model"
    result1.quality_metrics = {
        'completeness': 0.8,
        'relevance': 0.9,
        'accuracy': 0.7,
        'clarity': 0.85
    }

    result2 = AnalysisResult(topic_id="topic_2", node_id=node2_id)
    result2.set_content("This is test analysis content for topic 2. It provides comprehensive analysis.")
    result2.set_status(AnalysisStatus.COMPLETED)
    result2.metadata.model_name = "test_model"
    result2.quality_metrics = {
        'completeness': 0.9,
        'relevance': 0.8,
        'accuracy': 0.8,
        'clarity': 0.9
    }
    
    results[node1_id] = result1
    results[node2_id] = result2
    
    return hierarchy, results


def test_json_export(hierarchy, results, output_dir):
    """Test JSON export functionality."""
    print("Testing JSON export...")
    
    output_file = output_dir / "test_export.json"
    
    # Create export data (similar to GUI/CLI export)
    export_data = {
        'export_time': datetime.now().isoformat(),
        'hierarchy': hierarchy.to_dict(),
        'results': {node_id: result.to_dict() for node_id, result in results.items()}
    }
    
    # Write JSON
    write_json(output_file, export_data)
    
    # Verify file exists and is valid
    assert output_file.exists(), "JSON export file was not created"
    
    # Read back and verify
    loaded_data = read_json(output_file)
    assert 'export_time' in loaded_data, "Export time missing from JSON"
    assert 'hierarchy' in loaded_data, "Hierarchy missing from JSON"
    assert 'results' in loaded_data, "Results missing from JSON"
    assert len(loaded_data['results']) == len(results), "Incorrect number of results in JSON"
    
    print(f"✓ JSON export successful: {output_file}")
    return True


def test_csv_export(hierarchy, results, output_dir):
    """Test CSV export functionality."""
    print("Testing CSV export...")
    
    output_file = output_dir / "test_export.csv"
    
    # Export as CSV (similar to GUI/CLI export)
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Node ID', 'Status', 'Quality Score', 'Content Length', 'Content'])
        
        for node_id, result in results.items():
            quality_score = result.calculate_quality_score() if hasattr(result, 'calculate_quality_score') else 0
            writer.writerow([
                node_id,
                result.status.value,
                f"{quality_score:.2f}",
                len(result.content),
                result.content.replace('\n', ' ').replace('\r', '')
            ])
    
    # Verify file exists and has correct content
    assert output_file.exists(), "CSV export file was not created"
    
    # Read back and verify
    with open(output_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        rows = list(reader)
        
    assert len(rows) == len(results) + 1, "Incorrect number of rows in CSV (including header)"
    assert rows[0] == ['Node ID', 'Status', 'Quality Score', 'Content Length', 'Content'], "Incorrect CSV header"
    
    print(f"✓ CSV export successful: {output_file}")
    return True


def test_txt_export(hierarchy, results, output_dir):
    """Test TXT export functionality."""
    print("Testing TXT export...")
    
    output_file = output_dir / "test_export.txt"
    
    # Export as TXT (similar to GUI/CLI export)
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"Analysis Results Export\n")
        f.write(f"Generated: {datetime.now().isoformat()}\n")
        f.write(f"Total Results: {len(results)}\n")
        f.write("=" * 50 + "\n\n")

        for node_id, result in results.items():
            f.write(f"Node ID: {node_id}\n")
            f.write(f"Status: {result.status.value}\n")
            quality_score = result.calculate_quality_score() if hasattr(result, 'calculate_quality_score') else 0
            f.write(f"Quality Score: {quality_score:.2f}\n")
            f.write(f"Content Length: {len(result.content)}\n")
            f.write(f"Content:\n{result.content}\n")
            f.write("-" * 30 + "\n\n")
    
    # Verify file exists and has content
    assert output_file.exists(), "TXT export file was not created"
    
    content = output_file.read_text(encoding='utf-8')
    assert "Analysis Results Export" in content, "TXT export missing header"
    assert "Total Results:" in content, "TXT export missing results count"
    
    for node_id, result in results.items():
        assert node_id in content, f"Node ID {node_id} missing from TXT export"
        assert result.content in content, f"Result content missing from TXT export"
    
    print(f"✓ TXT export successful: {output_file}")
    return True


def test_output_directory_creation():
    """Test that output directories are created properly."""
    print("Testing output directory creation...")
    
    # Test with nested directory
    test_dir = Path(tempfile.gettempdir()) / "deep_research_test" / "nested" / "output"
    
    # Ensure it doesn't exist initially
    if test_dir.exists():
        shutil.rmtree(test_dir.parent.parent)
    
    # Create directory (like the export functions do)
    test_dir.mkdir(parents=True, exist_ok=True)
    
    assert test_dir.exists(), "Output directory was not created"
    assert test_dir.is_dir(), "Output path is not a directory"
    
    # Cleanup
    shutil.rmtree(test_dir.parent.parent)
    
    print("✓ Output directory creation successful")
    return True


def test_settings_output_dir():
    """Test that settings output directory is properly configured."""
    print("Testing settings output directory...")
    
    output_dir = settings.get("paths", "output_dir")
    assert output_dir is not None, "Output directory not configured in settings"
    
    # Ensure the directory exists
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    assert Path(output_dir).exists(), "Default output directory does not exist"
    
    print(f"✓ Settings output directory: {output_dir}")
    return True


def main():
    """Run all export functionality tests."""
    print("=" * 60)
    print("TESTING EXPORT FUNCTIONALITY")
    print("=" * 60)
    
    try:
        # Create temporary test directory
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = Path(temp_dir)
            
            # Create test data
            hierarchy, results = create_test_results()
            
            # Run tests
            tests = [
                lambda: test_settings_output_dir(),
                lambda: test_output_directory_creation(),
                lambda: test_json_export(hierarchy, results, output_dir),
                lambda: test_csv_export(hierarchy, results, output_dir),
                lambda: test_txt_export(hierarchy, results, output_dir),
            ]
            
            passed = 0
            failed = 0
            
            for test in tests:
                try:
                    test()
                    passed += 1
                except Exception as e:
                    print(f"✗ Test failed: {str(e)}")
                    failed += 1
            
            print("\n" + "=" * 60)
            print(f"TEST RESULTS: {passed} passed, {failed} failed")
            print("=" * 60)
            
            if failed == 0:
                print("🎉 All export functionality tests passed!")
                return True
            else:
                print("❌ Some tests failed. Check the output above.")
                return False
                
    except Exception as e:
        print(f"❌ Test setup failed: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
