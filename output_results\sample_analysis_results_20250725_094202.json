{"export_time": "2025-07-25T09:42:02.889894", "total_results": 3, "hierarchy": {"id": "5960da8a-ffc4-4018-aaf6-fc042aec71a0", "name": "AI Research Topics", "description": "Sample hierarchy for demonstration", "topics": {"c3f164e8-5f2e-47f7-bf32-989f6d4afb7a": {"id": "c3f164e8-5f2e-47f7-bf32-989f6d4afb7a", "name": "Artificial Intelligence", "description": "", "nodes": {"c8823fff-57e9-4504-9c79-ba9ad40adc0a": {"id": "c8823fff-57e9-4504-9c79-ba9ad40adc0a", "content": "Artificial Intelligence", "level": 0, "parent_id": null, "children_ids": ["38e14b72-5366-46cc-95c9-cfb2d38e28c8"], "metadata": {}, "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894"}, "38e14b72-5366-46cc-95c9-cfb2d38e28c8": {"id": "38e14b72-5366-46cc-95c9-cfb2d38e28c8", "content": "Machine Learning", "level": 1, "parent_id": "c8823fff-57e9-4504-9c79-ba9ad40adc0a", "children_ids": ["a7fd299d-307c-4ebe-9ec2-f23060a7a2c0"], "metadata": {}, "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894"}, "a7fd299d-307c-4ebe-9ec2-f23060a7a2c0": {"id": "a7fd299d-307c-4ebe-9ec2-f23060a7a2c0", "content": "Deep Learning", "level": 2, "parent_id": "38e14b72-5366-46cc-95c9-cfb2d38e28c8", "children_ids": [], "metadata": {}, "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894"}}, "root_ids": ["c8823fff-57e9-4504-9c79-ba9ad40adc0a"], "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894", "metadata": {}}, "0fd17e74-6588-4d4c-9889-1e4cf894e16d": {"id": "0fd17e74-6588-4d4c-9889-1e4cf894e16d", "name": "Artificial Intelligence", "description": "", "nodes": {"3f4a5320-4e11-497a-a674-c309de901b05": {"id": "3f4a5320-4e11-497a-a674-c309de901b05", "content": "Artificial Intelligence", "level": 0, "parent_id": null, "children_ids": ["32688dfa-d450-4109-b1fc-f81d92052283"], "metadata": {}, "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894"}, "32688dfa-d450-4109-b1fc-f81d92052283": {"id": "32688dfa-d450-4109-b1fc-f81d92052283", "content": "Natural Language Processing", "level": 1, "parent_id": "3f4a5320-4e11-497a-a674-c309de901b05", "children_ids": ["79f83a51-0c1b-42b0-ad24-0cd1c84f1fd1"], "metadata": {}, "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894"}, "79f83a51-0c1b-42b0-ad24-0cd1c84f1fd1": {"id": "79f83a51-0c1b-42b0-ad24-0cd1c84f1fd1", "content": "Transformers", "level": 2, "parent_id": "32688dfa-d450-4109-b1fc-f81d92052283", "children_ids": [], "metadata": {}, "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894"}}, "root_ids": ["3f4a5320-4e11-497a-a674-c309de901b05"], "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894", "metadata": {}}, "b5b194ca-527f-49d8-a8ed-0f703366392e": {"id": "b5b194ca-527f-49d8-a8ed-0f703366392e", "name": "Data Science", "description": "", "nodes": {"8a0bdbf2-12ec-4eb9-bd39-c616e78b03ca": {"id": "8a0bdbf2-12ec-4eb9-bd39-c616e78b03ca", "content": "Data Science", "level": 0, "parent_id": null, "children_ids": ["3e934033-b9ff-4c0f-82e6-456a1d337fd2"], "metadata": {}, "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894"}, "3e934033-b9ff-4c0f-82e6-456a1d337fd2": {"id": "3e934033-b9ff-4c0f-82e6-456a1d337fd2", "content": "Statistics", "level": 1, "parent_id": "8a0bdbf2-12ec-4eb9-bd39-c616e78b03ca", "children_ids": ["d36c0c7a-5ff5-420a-86a3-cde6e82f346e"], "metadata": {}, "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894"}, "d36c0c7a-5ff5-420a-86a3-cde6e82f346e": {"id": "d36c0c7a-5ff5-420a-86a3-cde6e82f346e", "content": "Regression Analysis", "level": 2, "parent_id": "3e934033-b9ff-4c0f-82e6-456a1d337fd2", "children_ids": [], "metadata": {}, "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894"}}, "root_ids": ["8a0bdbf2-12ec-4eb9-bd39-c616e78b03ca"], "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894", "metadata": {}}}, "levels": [{"level": 0, "name": "Topic", "description": "Main topic or subject area", "required": true, "max_length": 200, "validation_rules": {}}, {"level": 1, "name": "Category", "description": "Primary category or theme", "required": false, "max_length": 200, "validation_rules": {}}, {"level": 2, "name": "Subcategory", "description": "Secondary classification", "required": false, "max_length": 200, "validation_rules": {}}, {"level": 3, "name": "Aspect", "description": "Specific aspect or angle", "required": false, "max_length": 200, "validation_rules": {}}, {"level": 4, "name": "Detail", "description": "Detailed sub-aspect", "required": false, "max_length": 200, "validation_rules": {}}], "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894", "metadata": {}}, "results": {"c8823fff-57e9-4504-9c79-ba9ad40adc0a": {"id": "7abf09c6-0159-4b30-8920-b0f40109549f", "topic_id": "c3f164e8-5f2e-47f7-bf32-989f6d4afb7a", "node_id": "c8823fff-57e9-4504-9c79-ba9ad40adc0a", "status": "completed", "metadata": {"id": "c35ac73e-1fc2-4932-942f-4de883c7563c", "analysis_type": "recursive", "model_name": "llama3:latest", "model_version": "", "prompt_template": "", "parameters": {}, "created_at": "2025-07-25T09:42:02.889894", "started_at": null, "completed_at": "2025-07-25T09:42:02.889894", "duration": null}, "progress": {"current_step": 0, "total_steps": 0, "current_item": "Completed", "percentage": 0.0, "eta_seconds": 0.0, "items_per_second": 0.0, "status_message": "Analysis completed successfully", "last_updated": "2025-07-25T09:42:02.889894"}, "content": "Deep Learning Analysis:\n\nDeep learning is a subset of machine learning that uses artificial neural networks with multiple layers to model and understand complex patterns in data. Key characteristics include:\n\n1. Hierarchical Learning: Deep networks learn representations at multiple levels of abstraction\n2. Feature Extraction: Automatic feature learning from raw data\n3. Scalability: Performance improves with more data and computational power\n4. Applications: Computer vision, natural language processing, speech recognition\n\nRecent advances include transformer architectures, attention mechanisms, and self-supervised learning approaches.", "raw_response": "", "processed_data": {}, "confidence_score": 0.0, "quality_metrics": {"completeness": 0.92, "relevance": 0.95, "accuracy": 0.88, "clarity": 0.9}, "child_results": [], "parent_result_id": null, "errors": [], "warnings": [], "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894", "tags": [], "custom_metadata": {}}, "3f4a5320-4e11-497a-a674-c309de901b05": {"id": "c83ede3c-a63f-4ec4-ae43-5aa5659cf979", "topic_id": "0fd17e74-6588-4d4c-9889-1e4cf894e16d", "node_id": "3f4a5320-4e11-497a-a674-c309de901b05", "status": "completed", "metadata": {"id": "5f290f3e-31d4-4618-950a-469793b91395", "analysis_type": "recursive", "model_name": "llama3:latest", "model_version": "", "prompt_template": "", "parameters": {}, "created_at": "2025-07-25T09:42:02.889894", "started_at": null, "completed_at": "2025-07-25T09:42:02.889894", "duration": null}, "progress": {"current_step": 0, "total_steps": 0, "current_item": "Completed", "percentage": 0.0, "eta_seconds": 0.0, "items_per_second": 0.0, "status_message": "Analysis completed successfully", "last_updated": "2025-07-25T09:42:02.889894"}, "content": "Transformer Architecture Analysis:\n\nTransformers have revolutionized natural language processing through their attention-based architecture. Key innovations include:\n\n1. Self-Attention Mechanism: Allows models to weigh the importance of different words in context\n2. Parallel Processing: Unlike RNNs, transformers can process sequences in parallel\n3. Positional Encoding: Maintains sequence order information without recurrence\n4. Multi-Head Attention: Captures different types of relationships simultaneously\n\nApplications span from BERT and GPT models to recent large language models like ChatGPT and Claude.", "raw_response": "", "processed_data": {}, "confidence_score": 0.0, "quality_metrics": {"completeness": 0.89, "relevance": 0.93, "accuracy": 0.91, "clarity": 0.87}, "child_results": [], "parent_result_id": null, "errors": [], "warnings": [], "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894", "tags": [], "custom_metadata": {}}, "8a0bdbf2-12ec-4eb9-bd39-c616e78b03ca": {"id": "d92083fb-0ac4-4fd4-b727-0e318ac28f8a", "topic_id": "b5b194ca-527f-49d8-a8ed-0f703366392e", "node_id": "8a0bdbf2-12ec-4eb9-bd39-c616e78b03ca", "status": "completed", "metadata": {"id": "4bb7d7c5-bf0c-4cfc-a8f3-ccc6af31b664", "analysis_type": "recursive", "model_name": "llama3:latest", "model_version": "", "prompt_template": "", "parameters": {}, "created_at": "2025-07-25T09:42:02.889894", "started_at": null, "completed_at": "2025-07-25T09:42:02.889894", "duration": null}, "progress": {"current_step": 0, "total_steps": 0, "current_item": "Completed", "percentage": 0.0, "eta_seconds": 0.0, "items_per_second": 0.0, "status_message": "Analysis completed successfully", "last_updated": "2025-07-25T09:42:02.889894"}, "content": "Regression Analysis Overview:\n\nRegression analysis is a statistical method for modeling relationships between variables. Core concepts include:\n\n1. Linear Regression: Models linear relationships between dependent and independent variables\n2. Multiple Regression: Extends to multiple predictor variables\n3. Assumptions: Linearity, independence, homoscedasticity, normality\n4. Model Evaluation: R-squared, adjusted R-squared, residual analysis\n\nAdvanced techniques include polynomial regression, ridge regression, and lasso regression for handling complex datasets.", "raw_response": "", "processed_data": {}, "confidence_score": 0.0, "quality_metrics": {"completeness": 0.85, "relevance": 0.88, "accuracy": 0.92, "clarity": 0.89}, "child_results": [], "parent_result_id": null, "errors": [], "warnings": [], "created_at": "2025-07-25T09:42:02.889894", "updated_at": "2025-07-25T09:42:02.889894", "tags": [], "custom_metadata": {}}}}