# Recursive analysis logic
import time
from typing import List, Dict, Any, Optional, Callable, Iterator
from datetime import datetime
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed

from ..models.topic import Topic, TopicNode
from ..models.result import AnalysisResult, AnalysisStatus, AnalysisType
from ..models.hierarchy import Hierarchy
from ..ollama.api import get_ollama_client, OllamaAPIError
from ..ollama.model_manager import get_model_manager
from ..prompting.prompt_editor import PromptEditor
from ..utils.logging_config import AnalysisLogger
from ..utils.eta import get_eta_estimator
from ..config.settings import settings


class RecursiveAnalyzer:
    """Performs recursive depth-first analysis of hierarchical topics."""

    def __init__(self,
                 model_name: Optional[str] = None,
                 max_depth: int = 5,
                 max_concurrent: int = 3):
        self.ollama_client = get_ollama_client()
        self.model_manager = get_model_manager()
        self.model_name = model_name or settings.get("ollama", "default_model", "llama2")
        self.max_depth = max_depth
        self.max_concurrent = max_concurrent

        self.prompt_editor = PromptEditor()
        self.logger = AnalysisLogger("recursive")

        # Analysis state
        self.current_analysis_id: Optional[str] = None
        self.results: Dict[str, AnalysisResult] = {}
        self.cancelled = False

        # Progress tracking
        self.progress_callback: Optional[Callable] = None
        self.total_nodes = 0
        self.processed_nodes = 0
        self.eta_estimator = get_eta_estimator()
        self.current_task_id: Optional[str] = None

    def set_progress_callback(self, callback: Callable[[int, int, str], None]):
        """Set callback for progress updates."""
        self.progress_callback = callback

    def cancel_analysis(self):
        """Cancel the current analysis."""
        self.cancelled = True
        self.logger.logger.info("Analysis cancellation requested")

    def analyze_hierarchy(self,
                         hierarchy: Hierarchy,
                         system_prompt: Optional[str] = None,
                         custom_prompts: Optional[Dict[int, str]] = None,
                         analysis_options: Optional[Dict[str, Any]] = None) -> Dict[str, AnalysisResult]:
        """Analyze an entire hierarchy recursively."""

        self.logger.log_start("hierarchy", "recursive")
        start_time = time.time()

        # Reset state
        self.results.clear()
        self.cancelled = False
        self.processed_nodes = 0

        # Count total nodes for progress tracking
        self.total_nodes = sum(topic.get_node_count() for topic in hierarchy.topics.values())

        # Start ETA tracking
        self.current_task_id = f"recursive_analysis_{int(time.time())}"
        self.eta_estimator.start_task(
            self.current_task_id,
            "recursive",
            self.total_nodes,
            complexity={'hierarchy_size': len(hierarchy.topics), 'max_depth': self.max_depth}
        )

        try:
            # Validate model
            model_validation = self.model_manager.validate_model(self.model_name)
            if not model_validation['valid']:
                raise ValueError(f"Model validation failed: {model_validation['errors']}")

            # Process each topic in the hierarchy
            for topic_id, topic in hierarchy.topics.items():
                if self.cancelled:
                    break

                topic_results = self.analyze_topic(
                    topic,
                    system_prompt=system_prompt,
                    custom_prompts=custom_prompts,
                    analysis_options=analysis_options
                )

                # Merge results
                self.results.update(topic_results)

            duration = time.time() - start_time
            self.logger.log_completion("hierarchy", "recursive", duration)

            # Complete ETA tracking
            if self.current_task_id:
                self.eta_estimator.complete_task(self.current_task_id)

            return self.results

        except Exception as e:
            self.logger.log_error(e, "hierarchy analysis")
            raise

    def analyze_topic(self,
                     topic: Topic,
                     system_prompt: Optional[str] = None,
                     custom_prompts: Optional[Dict[int, str]] = None,
                     analysis_options: Optional[Dict[str, Any]] = None) -> Dict[str, AnalysisResult]:
        """Analyze a single topic recursively."""

        topic_results = {}

        # Process each root node
        for root_id in topic.root_ids:
            if self.cancelled:
                break

            root_results = self._analyze_node_recursive(
                topic,
                root_id,
                system_prompt=system_prompt,
                custom_prompts=custom_prompts,
                analysis_options=analysis_options
            )

            topic_results.update(root_results)

        return topic_results

    def _analyze_node_recursive(self,
                               topic: Topic,
                               node_id: str,
                               parent_context: str = "",
                               system_prompt: Optional[str] = None,
                               custom_prompts: Optional[Dict[int, str]] = None,
                               analysis_options: Optional[Dict[str, Any]] = None,
                               depth: int = 0) -> Dict[str, AnalysisResult]:
        """Recursively analyze a node and its children."""

        if self.cancelled or depth >= self.max_depth:
            return {}

        node = topic.get_node(node_id)
        if not node:
            return {}

        results = {}

        # Create analysis result for this node
        result = AnalysisResult(topic_id=topic.id, node_id=node_id)
        result.metadata.analysis_type = AnalysisType.RECURSIVE
        result.metadata.model_name = self.model_name
        result.set_status(AnalysisStatus.RUNNING)

        try:
            # Build context from parent path
            context_path = self._build_context_path(topic, node_id, parent_context)

            # Generate prompt for this level
            prompt = self._generate_prompt_for_node(
                node,
                context_path,
                custom_prompts,
                analysis_options
            )

            # Store prompt in metadata
            result.metadata.prompt_template = prompt

            # Perform analysis
            start_time = time.time()

            analysis_content = self._perform_node_analysis(
                prompt,
                system_prompt,
                analysis_options
            )

            response_time = time.time() - start_time

            # Update result
            result.set_content(analysis_content)
            result.set_status(AnalysisStatus.COMPLETED)

            # Update performance metrics
            self.model_manager.update_model_performance(
                self.model_name,
                response_time,
                len(analysis_content.split()),
                True
            )

            # Update progress
            self.processed_nodes += 1

            # Update ETA estimator
            if self.current_task_id:
                eta_info = self.eta_estimator.update_task_progress(
                    self.current_task_id,
                    self.processed_nodes,
                    response_time
                )

                # Enhanced progress callback with ETA
                if self.progress_callback:
                    status_msg = f"Analyzed: {node.content[:50]}... | ETA: {eta_info['eta_formatted']} | {eta_info['items_per_second']:.1f} nodes/sec"
                    self.progress_callback(
                        self.processed_nodes,
                        self.total_nodes,
                        status_msg
                    )
            elif self.progress_callback:
                # Fallback without ETA
                self.progress_callback(
                    self.processed_nodes,
                    self.total_nodes,
                    f"Analyzed: {node.content[:50]}..."
                )

            results[node_id] = result

            # Recursively analyze children
            for child_id in node.children_ids:
                if self.cancelled:
                    break

                child_results = self._analyze_node_recursive(
                    topic,
                    child_id,
                    parent_context=context_path,
                    system_prompt=system_prompt,
                    custom_prompts=custom_prompts,
                    analysis_options=analysis_options,
                    depth=depth + 1
                )

                # Link child results to parent
                for child_result_id, child_result in child_results.items():
                    child_result.set_parent_result(node_id)
                    result.add_child_result(child_result_id)

                results.update(child_results)

        except Exception as e:
            result.set_status(AnalysisStatus.FAILED)
            result.add_error(str(e))
            self.logger.log_error(e, f"node {node_id}")

            # Update performance metrics for failure
            self.model_manager.update_model_performance(
                self.model_name, 0, 0, False
            )

            results[node_id] = result

        return results

    def _build_context_path(self, topic: Topic, node_id: str, parent_context: str = "") -> str:
        """Build context path from root to current node."""
        path_nodes = topic.get_path_to_root(node_id)

        if parent_context:
            context_parts = [parent_context]
        else:
            context_parts = []

        for node in path_nodes:
            context_parts.append(f"Level {node.level}: {node.content}")

        return " → ".join(context_parts)

    def _generate_prompt_for_node(self,
                                 node: TopicNode,
                                 context_path: str,
                                 custom_prompts: Optional[Dict[int, str]] = None,
                                 analysis_options: Optional[Dict[str, Any]] = None) -> str:
        """Generate analysis prompt for a specific node."""

        # Check for custom prompt for this level
        if custom_prompts and node.level in custom_prompts:
            base_prompt = custom_prompts[node.level]
        else:
            # Use default prompt based on level
            base_prompt = self._get_default_prompt_for_level(node.level)

        # Build full prompt with context
        full_prompt = f"""Context Path: {context_path}

Current Topic: {node.content}
Level: {node.level}

{base_prompt}

Please provide a comprehensive analysis focusing on this specific aspect within the given context."""

        # Add analysis options if specified
        if analysis_options:
            if analysis_options.get('include_examples', False):
                full_prompt += "\n\nPlease include relevant examples in your analysis."

            if analysis_options.get('focus_areas'):
                focus_areas = ", ".join(analysis_options['focus_areas'])
                full_prompt += f"\n\nPay special attention to: {focus_areas}"

            if analysis_options.get('output_format'):
                format_instruction = analysis_options['output_format']
                full_prompt += f"\n\nFormat your response as: {format_instruction}"

        return full_prompt

    def _get_default_prompt_for_level(self, level: int) -> str:
        """Get default prompt template for a specific hierarchy level."""
        default_prompts = {
            0: "Provide a comprehensive overview of this main topic. Include key concepts, importance, and main areas of focus.",
            1: "Analyze this category within the broader topic context. Explain its significance, key components, and relationships.",
            2: "Examine this subcategory in detail. Discuss specific aspects, methodologies, or approaches relevant to this area.",
            3: "Provide detailed analysis of this specific aspect. Include technical details, applications, and practical considerations.",
            4: "Give an in-depth examination of this detailed element. Focus on specifics, nuances, and expert-level insights."
        }

        return default_prompts.get(level, "Analyze this topic thoroughly and provide detailed insights.")

    def _perform_node_analysis(self,
                              prompt: str,
                              system_prompt: Optional[str] = None,
                              analysis_options: Optional[Dict[str, Any]] = None) -> str:
        """Perform the actual analysis for a node."""

        # Prepare generation options
        generation_options = {
            'temperature': 0.7,
            'top_p': 0.9,
            'num_predict': 500
        }

        # Override with analysis options
        if analysis_options and 'generation_options' in analysis_options:
            generation_options.update(analysis_options['generation_options'])

        try:
            # Generate response
            response = self.ollama_client.generate_simple(
                model=self.model_name,
                prompt=prompt,
                system=system_prompt,
                options=generation_options
            )

            return response

        except OllamaAPIError as e:
            raise Exception(f"Ollama API error: {str(e)}")
        except Exception as e:
            raise Exception(f"Analysis generation failed: {str(e)}")

    def get_analysis_summary(self) -> Dict[str, Any]:
        """Get summary of the current analysis results."""
        if not self.results:
            return {'status': 'no_results'}

        total_results = len(self.results)
        completed_results = sum(1 for r in self.results.values() if r.status == AnalysisStatus.COMPLETED)
        failed_results = sum(1 for r in self.results.values() if r.status == AnalysisStatus.FAILED)

        # Calculate average quality score
        quality_scores = [r.calculate_quality_score() for r in self.results.values() if r.status == AnalysisStatus.COMPLETED]
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0

        # Calculate total content length
        total_content_length = sum(len(r.content) for r in self.results.values())

        return {
            'status': 'completed' if not self.cancelled else 'cancelled',
            'total_results': total_results,
            'completed_results': completed_results,
            'failed_results': failed_results,
            'success_rate': completed_results / total_results if total_results > 0 else 0.0,
            'average_quality_score': avg_quality,
            'total_content_length': total_content_length,
            'model_used': self.model_name,
            'analysis_type': 'recursive'
        }


# Convenience functions
def analyze_hierarchy_recursive(hierarchy: Hierarchy,
                               model_name: Optional[str] = None,
                               **kwargs) -> Dict[str, AnalysisResult]:
    """Convenience function to analyze hierarchy recursively."""
    analyzer = RecursiveAnalyzer(model_name=model_name)
    return analyzer.analyze_hierarchy(hierarchy, **kwargs)

def analyze_topic_recursive(topic: Topic,
                           model_name: Optional[str] = None,
                           **kwargs) -> Dict[str, AnalysisResult]:
    """Convenience function to analyze topic recursively."""
    analyzer = RecursiveAnalyzer(model_name=model_name)
    return analyzer.analyze_topic(topic, **kwargs)
