#!/usr/bin/env python3
"""
Integration test to verify export functionality works with real data.
This creates a small hierarchy, runs a mock analysis, and tests export.
"""

import os
import sys
import json
import tempfile
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config.settings import settings
from src.models.hierarchy import Hierarchy
from src.models.result import AnalysisResult, AnalysisStatus
from src.cli.interactive import CLIInterface
from src.utils.file_io import write_json, read_json


def test_cli_export_integration():
    """Test CLI export functionality with real data."""
    print("Testing CLI export integration...")
    
    # Create a CLI interface instance
    cli = CLIInterface()
    
    # Create a test hierarchy
    hierarchy = Hierarchy("Integration Test", "Test hierarchy for export integration")
    topic = hierarchy.create_topic_from_layers(["Technology", "AI", "Machine Learning"])
    cli.hierarchy = hierarchy
    
    # Create mock analysis results
    node_id = list(topic.nodes.keys())[0]
    result = AnalysisResult(topic_id=topic.id, node_id=node_id)
    result.set_content("This is a test analysis result for integration testing.")
    result.set_status(AnalysisStatus.COMPLETED)
    result.metadata.model_name = "test_model"
    result.quality_metrics = {'completeness': 0.8, 'relevance': 0.9}
    
    cli.analysis_results[node_id] = result
    
    # Test export functionality
    output_dir = settings.get("paths", "output_dir")
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Test JSON export
    json_file = Path(output_dir) / "integration_test.json"
    export_data = {
        'export_time': '2025-01-01T00:00:00',
        'total_results': len(cli.analysis_results),
        'hierarchy': cli.hierarchy.to_dict(),
        'results': {node_id: result.to_dict() for node_id, result in cli.analysis_results.items()}
    }
    write_json(json_file, export_data)
    
    # Verify export
    assert json_file.exists(), "JSON export file was not created"
    loaded_data = read_json(json_file)
    assert 'hierarchy' in loaded_data, "Hierarchy missing from export"
    assert 'results' in loaded_data, "Results missing from export"
    
    # Cleanup
    json_file.unlink()
    
    print("✓ CLI export integration test passed")
    return True


def test_output_directory_functionality():
    """Test output directory creation and access."""
    print("Testing output directory functionality...")
    
    # Get current output directory
    output_dir = settings.get("paths", "output_dir")
    print(f"Current output directory: {output_dir}")
    
    # Ensure it exists
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    assert Path(output_dir).exists(), "Output directory does not exist"
    
    # Test writing a file
    test_file = Path(output_dir) / "test_file.txt"
    test_file.write_text("Test content")
    assert test_file.exists(), "Could not write to output directory"
    
    # Cleanup
    test_file.unlink()
    
    print("✓ Output directory functionality test passed")
    return True


def test_settings_configuration():
    """Test that settings are properly configured."""
    print("Testing settings configuration...")
    
    # Test all required settings exist
    required_settings = [
        ("paths", "output_dir"),
        ("paths", "log_dir"),
        ("paths", "config_dir"),
        ("app", "name"),
        ("app", "version"),
        ("ollama", "base_url"),
        ("ollama", "default_model"),
        ("analysis", "default_type"),
        ("analysis", "max_depth"),
        ("logging", "level"),
        ("logging", "file_logging"),
    ]
    
    for section, key in required_settings:
        value = settings.get(section, key)
        assert value is not None, f"Setting {section}.{key} is not configured"
        print(f"✓ {section}.{key} = {value}")
    
    print("✓ Settings configuration test passed")
    return True


def test_file_format_exports():
    """Test all supported export formats."""
    print("Testing all export formats...")
    
    # Create test data
    hierarchy = Hierarchy("Format Test", "Test all export formats")
    topic = hierarchy.create_topic_from_layers(["Test", "Export", "Formats"])
    
    node_id = list(topic.nodes.keys())[0]
    result = AnalysisResult(topic_id=topic.id, node_id=node_id)
    result.set_content("Test content for format testing.")
    result.set_status(AnalysisStatus.COMPLETED)
    
    results = {node_id: result}
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Test JSON format
        json_file = temp_path / "test.json"
        export_data = {
            'hierarchy': hierarchy.to_dict(),
            'results': {node_id: result.to_dict() for node_id, result in results.items()}
        }
        write_json(json_file, export_data)
        assert json_file.exists() and json_file.stat().st_size > 0
        
        # Test CSV format
        csv_file = temp_path / "test.csv"
        import csv
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Node ID', 'Status', 'Content'])
            for node_id, result in results.items():
                writer.writerow([node_id, result.status.value, result.content])
        assert csv_file.exists() and csv_file.stat().st_size > 0
        
        # Test TXT format
        txt_file = temp_path / "test.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write("Test Export\n")
            for node_id, result in results.items():
                f.write(f"Node: {node_id}\nContent: {result.content}\n")
        assert txt_file.exists() and txt_file.stat().st_size > 0
    
    print("✓ All export formats test passed")
    return True


def main():
    """Run all integration tests."""
    print("=" * 60)
    print("EXPORT FUNCTIONALITY INTEGRATION TESTS")
    print("=" * 60)
    
    tests = [
        test_settings_configuration,
        test_output_directory_functionality,
        test_file_format_exports,
        test_cli_export_integration,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"✗ Test failed: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"INTEGRATION TEST RESULTS: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 All integration tests passed!")
        print("\nThe export functionality is working correctly:")
        print("- Output file naming works")
        print("- Results can be saved to output folder")
        print("- All export formats (JSON, CSV, TXT) work")
        print("- Settings are properly configured")
        print("- CLI export functionality works")
        return True
    else:
        print("❌ Some integration tests failed.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
