# Application configuration and constants
import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

class Settings:
    """Application settings and configuration management."""

    def __init__(self, config_file: Optional[str] = None):
        """Initialize settings with optional config file."""
        self.config_file = config_file
        self._config = self._load_default_config()

        # Load from config file if provided
        if config_file and os.path.exists(config_file):
            self._load_config_file(config_file)

        # Override with environment variables
        self._load_env_variables()

    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration values."""
        project_root = Path(__file__).parent.parent.parent

        return {
            # Application settings
            "app": {
                "name": "Deep Research Tool",
                "version": "0.1.0",
                "debug": False,
                "max_hierarchy_depth": 5,
                "auto_save": True,
                "theme": "default",
            },

            # File paths
            "paths": {
                "project_root": str(project_root),
                "output_dir": str(project_root / "output_results"),
                "logs_dir": str(project_root / "logs"),
                "log_dir": str(project_root / "logs"),  # Alternative name for compatibility
                "examples_dir": str(project_root / "examples"),
                "default_input_file": str(project_root / "input_topics_subangles_example.csv"),
                "config_dir": str(project_root / "config"),
            },

            # Ollama API settings
            "ollama": {
                "base_url": "http://localhost:11434",
                "timeout": 30,
                "max_retries": 3,
                "retry_delay": 1.0,
                "default_model": "llama3:latest",
                "stream": True,
                "stream_responses": True,
            },

            # Analysis settings
            "analysis": {
                "max_concurrent_requests": 3,
                "batch_size": 10,
                "enable_recursive": True,
                "enable_iterative": True,
                "max_iterations": 5,
                "convergence_threshold": 0.95,
                "default_type": "recursive",
                "max_depth": 5,
                "enable_quality_metrics": True,
            },

            # CLI settings
            "cli": {
                "progress_bar_width": 50,
                "show_eta": True,
                "colored_output": True,
                "verbose": False,
            },

            # GUI settings
            "gui": {
                "window_width": 1200,
                "window_height": 800,
                "theme": "default",
                "auto_save": True,
                "mindmap_layout": "spring",
            },

            # Logging settings
            "logging": {
                "level": "INFO",
                "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
                "rotation": "10 MB",
                "retention": "30 days",
                "enable_file_logging": True,
                "enable_console_logging": True,
                "file_logging": True,
                "console_logging": True,
                "max_file_size": "10MB",
            },

            # Plugin settings
            "plugins": {
                "enabled": True,
                "auto_load": True,
                "auto_discover": True,
                "plugin_dirs": ["src/plugins", "~/.deep_research_tool/plugins"],
                "directories": [
                    "./plugins",
                    "./src/plugins/builtin"
                ],
                "enabled_plugins": [
                    "example_analysis",
                    "fast_analysis",
                    "csv_export",
                    "tsv_export"
                ],
                "configs": {
                    "example_analysis": {
                        "prefix": "Example Analysis:",
                        "delay": 0.1
                    },
                    "fast_analysis": {
                        "max_words": 20
                    },
                    "csv_export": {
                        "delimiter": ",",
                        "include_metadata": True,
                        "include_quality": True
                    },
                    "tsv_export": {
                        "include_metadata": True,
                        "include_quality": True
                    }
                }
            }
        }

    def _load_config_file(self, config_file: str) -> None:
        """Load configuration from YAML file."""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = yaml.safe_load(f)
                if file_config:
                    self._merge_config(self._config, file_config)
        except Exception as e:
            print(f"Warning: Could not load config file {config_file}: {e}")

    def _load_env_variables(self) -> None:
        """Load configuration from environment variables."""
        env_mappings = {
            "DEEP_RESEARCH_DEBUG": ("app", "debug", bool),
            "DEEP_RESEARCH_OUTPUT_DIR": ("paths", "output_dir", str),
            "OLLAMA_BASE_URL": ("ollama", "base_url", str),
            "OLLAMA_TIMEOUT": ("ollama", "timeout", int),
            "OLLAMA_DEFAULT_MODEL": ("ollama", "default_model", str),
            "LOG_LEVEL": ("logging", "level", str),
            "CLI_VERBOSE": ("cli", "verbose", bool),
        }

        for env_var, (section, key, type_func) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    if type_func == bool:
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    else:
                        value = type_func(value)
                    self._config[section][key] = value
                except (ValueError, TypeError) as e:
                    print(f"Warning: Invalid value for {env_var}: {e}")

    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> None:
        """Recursively merge configuration dictionaries."""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value

    def get(self, section: str, key: str, default: Any = None) -> Any:
        """Get configuration value by section and key."""
        return self._config.get(section, {}).get(key, default)

    def get_section(self, section: str) -> Dict[str, Any]:
        """Get entire configuration section."""
        return self._config.get(section, {})

    def set(self, section: str, key: str, value: Any) -> None:
        """Set configuration value."""
        if section not in self._config:
            self._config[section] = {}
        self._config[section][key] = value

    def save_config(self, config_file: str) -> None:
        """Save current configuration to file."""
        try:
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self._config, f, default_flow_style=False, indent=2)
        except Exception as e:
            print(f"Error saving config file {config_file}: {e}")

    @property
    def config(self) -> Dict[str, Any]:
        """Get the full configuration dictionary."""
        return self._config.copy()

# Global settings instance
settings = Settings()

# Convenience functions for common settings
def get_output_dir() -> str:
    """Get the output directory path."""
    return settings.get("paths", "output_dir")

def get_logs_dir() -> str:
    """Get the logs directory path."""
    return settings.get("paths", "logs_dir")

def get_ollama_base_url() -> str:
    """Get the Ollama API base URL."""
    return settings.get("ollama", "base_url")

def get_max_hierarchy_depth() -> int:
    """Get the maximum hierarchy depth."""
    return settings.get("app", "max_hierarchy_depth")

def is_debug_mode() -> bool:
    """Check if debug mode is enabled."""
    return settings.get("app", "debug", False)
