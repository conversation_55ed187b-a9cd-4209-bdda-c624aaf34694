["tests/test_models.py::TestAnalysisResult::test_create_analysis_result", "tests/test_models.py::TestAnalysisResult::test_result_content_management", "tests/test_models.py::TestAnalysisResult::test_result_error_handling", "tests/test_models.py::TestAnalysisResult::test_result_export", "tests/test_models.py::TestAnalysisResult::test_result_progress_tracking", "tests/test_models.py::TestAnalysisResult::test_result_quality_calculation", "tests/test_models.py::TestAnalysisResult::test_result_serialization", "tests/test_models.py::TestAnalysisResult::test_result_status_management", "tests/test_models.py::TestHierarchy::test_configure_levels", "tests/test_models.py::TestHierarchy::test_create_hierarchy", "tests/test_models.py::TestHierarchy::test_create_topic_from_layers", "tests/test_models.py::TestHierarchy::test_csv_import_export", "tests/test_models.py::TestHierarchy::test_hierarchy_statistics", "tests/test_models.py::TestHierarchy::test_hierarchy_validation", "tests/test_models.py::TestTopic::test_add_nodes", "tests/test_models.py::TestTopic::test_create_topic", "tests/test_models.py::TestTopic::test_node_validation", "tests/test_models.py::TestTopic::test_remove_nodes", "tests/test_models.py::TestTopic::test_topic_queries", "tests/test_models.py::TestTopic::test_topic_serialization", "tests/test_models.py::TestTopicNode::test_add_remove_children", "tests/test_models.py::TestTopicNode::test_create_topic_node", "tests/test_models.py::TestTopicNode::test_serialization", "tests/test_models.py::TestTopicNode::test_topic_node_validation"]