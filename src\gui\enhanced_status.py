# Enhanced status display widget
import tkinter as tk
from tkinter import ttk
from datetime import datetime
from typing import Optional, Dict, Any
import threading
import time

from ..utils.logging_config import get_logger

logger = get_logger("gui.enhanced_status")


class EnhancedStatusWidget:
    """Enhanced status widget with detailed progress information."""
    
    def __init__(self, parent):
        self.parent = parent
        self.current_operation: Optional[str] = None
        self.start_time: Optional[datetime] = None
        self.last_update_time: Optional[datetime] = None
        self.total_items = 0
        self.completed_items = 0
        self.items_per_second = 0.0
        self.eta_text = ""
        
        # Create the widget
        self._setup_widget()
        
        # Start update timer
        self._schedule_updates()
    
    def _setup_widget(self):
        """Setup the enhanced status widget."""
        # Main frame
        self.main_frame = ttk.LabelFrame(self.parent, text="Analysis Status", padding=10)
        self.main_frame.pack(fill=tk.X, padx=5, pady=2)
        
        # Top row - Operation and overall progress
        top_frame = ttk.Frame(self.main_frame)
        top_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Operation label
        self.operation_label = ttk.Label(top_frame, text="Ready", 
                                       font=("TkDefaultFont", 10, "bold"))
        self.operation_label.pack(side=tk.LEFT)
        
        # Status indicator
        self.status_indicator = tk.Canvas(top_frame, width=12, height=12, 
                                        highlightthickness=0)
        self.status_indicator.pack(side=tk.LEFT, padx=(10, 0))
        self._update_status_indicator("ready")
        
        # Progress info on the right
        progress_info_frame = ttk.Frame(top_frame)
        progress_info_frame.pack(side=tk.RIGHT)
        
        self.progress_label = ttk.Label(progress_info_frame, text="0/0 (0%)")
        self.progress_label.pack(side=tk.RIGHT)
        
        # Middle row - Progress bar
        progress_frame = ttk.Frame(self.main_frame)
        progress_frame.pack(fill=tk.X, pady=2)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=300,
            mode='determinate'
        )
        self.progress_bar.pack(fill=tk.X)
        
        # Bottom row - Detailed info
        detail_frame = ttk.Frame(self.main_frame)
        detail_frame.pack(fill=tk.X, pady=(5, 0))
        
        # Left side - Speed and ETA
        left_detail_frame = ttk.Frame(detail_frame)
        left_detail_frame.pack(side=tk.LEFT)
        
        self.speed_label = ttk.Label(left_detail_frame, text="", 
                                   font=("TkDefaultFont", 8))
        self.speed_label.pack(side=tk.LEFT)
        
        self.eta_label = ttk.Label(left_detail_frame, text="", 
                                 font=("TkDefaultFont", 8), foreground="blue")
        self.eta_label.pack(side=tk.LEFT, padx=(15, 0))
        
        # Right side - Elapsed time
        self.elapsed_label = ttk.Label(detail_frame, text="", 
                                     font=("TkDefaultFont", 8))
        self.elapsed_label.pack(side=tk.RIGHT)
        
        # Current task description
        self.task_frame = ttk.Frame(self.main_frame)
        self.task_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.task_label = ttk.Label(self.task_frame, text="", 
                                  font=("TkDefaultFont", 8), 
                                  foreground="gray")
        self.task_label.pack(side=tk.LEFT)
    
    def _update_status_indicator(self, status: str):
        """Update the status indicator color."""
        self.status_indicator.delete("all")
        
        colors = {
            "ready": "#90EE90",      # Light green
            "running": "#FFD700",    # Gold
            "complete": "#32CD32",   # Lime green
            "error": "#FF6347",      # Tomato red
            "cancelled": "#FFA500"   # Orange
        }
        
        color = colors.get(status, "#D3D3D3")  # Light gray default
        self.status_indicator.create_oval(2, 2, 10, 10, fill=color, outline=color)
    
    def start_operation(self, operation_name: str, total_items: int = 0):
        """Start a new operation."""
        self.current_operation = operation_name
        self.start_time = datetime.now()
        self.last_update_time = self.start_time
        self.total_items = total_items
        self.completed_items = 0
        self.items_per_second = 0.0
        self.eta_text = "Calculating..."
        
        self.operation_label.config(text=operation_name)
        self._update_status_indicator("running")
        
        logger.info(f"Started operation: {operation_name} with {total_items} items")
    
    def update_progress(self, completed: int, total: int = None, 
                       current_task: str = "", eta: str = "", speed: float = 0.0):
        """Update progress information."""
        if total is not None:
            self.total_items = total
        
        self.completed_items = completed
        self.last_update_time = datetime.now()
        
        if speed > 0:
            self.items_per_second = speed
        elif self.start_time:
            elapsed = (self.last_update_time - self.start_time).total_seconds()
            if elapsed > 0 and completed > 0:
                self.items_per_second = completed / elapsed
        
        if eta:
            self.eta_text = eta
        
        # Update current task
        if current_task:
            self.task_label.config(text=current_task[:80] + "..." if len(current_task) > 80 else current_task)
        
        logger.debug(f"Progress updated: {completed}/{self.total_items} - {current_task}")
    
    def complete_operation(self, success: bool = True, message: str = ""):
        """Complete the current operation."""
        if success:
            self._update_status_indicator("complete")
            self.operation_label.config(text=f"{self.current_operation} - Complete")
            if message:
                self.task_label.config(text=message)
        else:
            self._update_status_indicator("error")
            self.operation_label.config(text=f"{self.current_operation} - Failed")
            if message:
                self.task_label.config(text=f"Error: {message}")
        
        logger.info(f"Operation completed: {self.current_operation}, success: {success}")
    
    def cancel_operation(self, message: str = ""):
        """Cancel the current operation."""
        self._update_status_indicator("cancelled")
        self.operation_label.config(text=f"{self.current_operation} - Cancelled")
        if message:
            self.task_label.config(text=message)
        
        logger.info(f"Operation cancelled: {self.current_operation}")
    
    def reset(self):
        """Reset to ready state."""
        self.current_operation = None
        self.start_time = None
        self.last_update_time = None
        self.total_items = 0
        self.completed_items = 0
        self.items_per_second = 0.0
        self.eta_text = ""
        
        self.operation_label.config(text="Ready")
        self._update_status_indicator("ready")
        self.progress_var.set(0)
        self.progress_label.config(text="0/0 (0%)")
        self.speed_label.config(text="")
        self.eta_label.config(text="")
        self.elapsed_label.config(text="")
        self.task_label.config(text="")
    
    def _update_display(self):
        """Update the display with current information."""
        if not self.current_operation:
            return
        
        # Update progress bar and label
        if self.total_items > 0:
            percentage = (self.completed_items / self.total_items) * 100
            self.progress_var.set(percentage)
            self.progress_label.config(text=f"{self.completed_items}/{self.total_items} ({percentage:.1f}%)")
        else:
            self.progress_var.set(0)
            self.progress_label.config(text=f"{self.completed_items}/? (?%)")
        
        # Update speed
        if self.items_per_second > 0:
            if self.items_per_second >= 1:
                self.speed_label.config(text=f"Speed: {self.items_per_second:.1f} items/sec")
            else:
                self.speed_label.config(text=f"Speed: {self.items_per_second:.2f} items/sec")
        else:
            self.speed_label.config(text="")
        
        # Update ETA
        if self.eta_text:
            self.eta_label.config(text=f"ETA: {self.eta_text}")
        else:
            self.eta_label.config(text="")
        
        # Update elapsed time
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            elapsed_str = str(elapsed).split('.')[0]  # Remove microseconds
            self.elapsed_label.config(text=f"Elapsed: {elapsed_str}")
    
    def _schedule_updates(self):
        """Schedule regular display updates."""
        self._update_display()
        # Schedule next update
        self.parent.after(1000, self._schedule_updates)  # Update every second
