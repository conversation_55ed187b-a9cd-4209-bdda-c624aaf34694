# Hierarchy Explorer GUI component
import tkinter as tk
from tkinter import ttk
from typing import Optional, Callable, Dict, Any

from ..models.hierarchy import Hierarchy
from ..models.topic import Topic, TopicNode
from ..utils.logging_config import get_logger

logger = get_logger("gui.hierarchy_explorer")


class HierarchyExplorer:
    """GUI component for exploring and visualizing hierarchies."""
    
    def __init__(self, parent, on_node_selected: Optional[Callable[[str], None]] = None):
        self.parent = parent
        self.on_node_selected = on_node_selected
        self.hierarchy: Optional[Hierarchy] = None
        
        # Create the main frame
        self.main_frame = ttk.Frame(parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Setup the tree view
        self._setup_tree_view()
        
        # Setup context menu
        self._setup_context_menu()
        
        logger.debug("HierarchyExplorer initialized")
    
    def _setup_tree_view(self):
        """Setup the tree view for hierarchy display."""
        # Create tree view with columns (including hidden columns for data storage)
        columns = ("Type", "Level", "Children", "node_id", "topic_id")
        self.tree = ttk.Treeview(self.main_frame, columns=columns, show="tree headings")
        
        # Configure headings
        self.tree.heading("#0", text="Content")
        self.tree.heading("Type", text="Type")
        self.tree.heading("Level", text="Level")
        self.tree.heading("Children", text="Children")

        # Configure hidden column headings (empty text)
        self.tree.heading("node_id", text="")
        self.tree.heading("topic_id", text="")
        
        # Configure columns
        self.tree.column("#0", width=300, minwidth=200)
        self.tree.column("Type", width=80, minwidth=60)
        self.tree.column("Level", width=60, minwidth=40)
        self.tree.column("Children", width=80, minwidth=60)

        # Configure hidden columns for data storage (width=0 makes them invisible)
        self.tree.column("node_id", width=0, minwidth=0)
        self.tree.column("topic_id", width=0, minwidth=0)
        
        # Create scrollbars
        v_scrollbar = ttk.Scrollbar(self.main_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(self.main_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        
        # Configure tree scrolling
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack tree and scrollbars
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Configure grid weights
        self.main_frame.grid_rowconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # Bind events
        self.tree.bind("<<TreeviewSelect>>", self._on_tree_selection)
        self.tree.bind("<Button-3>", self._on_right_click)  # Right-click for context menu
        self.tree.bind("<Double-1>", self._on_double_click)
    
    def _setup_context_menu(self):
        """Setup context menu for tree items."""
        self.context_menu = tk.Menu(self.tree, tearoff=0)
        self.context_menu.add_command(label="Expand All", command=self._expand_all)
        self.context_menu.add_command(label="Collapse All", command=self._collapse_all)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="View Details", command=self._view_details)
        self.context_menu.add_command(label="Copy Node ID", command=self._copy_node_id)
    
    def load_hierarchy(self, hierarchy: Hierarchy):
        """Load and display a hierarchy."""
        self.hierarchy = hierarchy
        self.clear()
        
        if not hierarchy:
            return
        
        logger.info(f"Loading hierarchy: {hierarchy.name}")
        
        # Add hierarchy root
        hierarchy_item = self.tree.insert("", "end", 
                                        text=f"📁 {hierarchy.name}",
                                        values=("Hierarchy", "-", len(hierarchy.topics)),
                                        tags=("hierarchy",))
        
        # Add topics
        for topic_id, topic in hierarchy.topics.items():
            self._add_topic_to_tree(hierarchy_item, topic)
        
        # Expand the hierarchy root
        self.tree.item(hierarchy_item, open=True)
        
        logger.info(f"Loaded {len(hierarchy.topics)} topics")
    
    def _add_topic_to_tree(self, parent_item, topic: Topic):
        """Add a topic and its nodes to the tree."""
        # Add topic item
        topic_item = self.tree.insert(parent_item, "end",
                                    text=f"📄 {topic.name}",
                                    values=("Topic", "-", topic.get_node_count()),
                                    tags=("topic",))
        
        # Add root nodes
        for root_id in topic.root_ids:
            self._add_node_to_tree(topic_item, topic, root_id)
        
        # Store topic reference
        self.tree.set(topic_item, "topic_id", topic.id)
    
    def _add_node_to_tree(self, parent_item, topic: Topic, node_id: str, max_depth: int = 10):
        """Recursively add nodes to the tree."""
        node = topic.get_node(node_id)
        if not node or node.level > max_depth:
            return
        
        # Determine icon based on node type
        if node.is_root():
            icon = "🌳"
        elif node.is_leaf():
            icon = "🍃"
        else:
            icon = "🌿"
        
        # Create display text
        display_text = node.content
        if len(display_text) > 50:
            display_text = display_text[:47] + "..."
        
        # Add node to tree
        node_item = self.tree.insert(parent_item, "end",
                                   text=f"{icon} {display_text}",
                                   values=("Node", node.level, len(node.children_ids)),
                                   tags=("node",))
        
        # Store node reference
        self.tree.set(node_item, "node_id", node_id)
        self.tree.set(node_item, "topic_id", topic.id)
        
        # Add children
        for child_id in node.children_ids:
            self._add_node_to_tree(node_item, topic, child_id, max_depth)
    
    def clear(self):
        """Clear the tree view."""
        for item in self.tree.get_children():
            self.tree.delete(item)
        logger.debug("Tree view cleared")
    
    def refresh(self):
        """Refresh the tree view."""
        if self.hierarchy:
            self.load_hierarchy(self.hierarchy)
    
    def expand_all(self):
        """Expand all tree items."""
        self._expand_all()
    
    def collapse_all(self):
        """Collapse all tree items."""
        self._collapse_all()
    
    def select_node(self, node_id: str):
        """Select a specific node in the tree."""
        for item in self._get_all_items():
            if self.tree.set(item, "node_id") == node_id:
                self.tree.selection_set(item)
                self.tree.focus(item)
                self.tree.see(item)
                break
    
    def highlight_nodes(self, node_ids: list, tag: str = "highlight"):
        """Highlight specific nodes in the tree."""
        # Remove existing highlights
        for item in self._get_all_items():
            tags = list(self.tree.item(item, "tags"))
            if tag in tags:
                tags.remove(tag)
                self.tree.item(item, tags=tags)
        
        # Add new highlights
        for item in self._get_all_items():
            if self.tree.set(item, "node_id") in node_ids:
                tags = list(self.tree.item(item, "tags"))
                tags.append(tag)
                self.tree.item(item, tags=tags)
        
        # Configure highlight appearance
        self.tree.tag_configure(tag, background="yellow")
    
    def get_selected_node_id(self) -> Optional[str]:
        """Get the currently selected node ID."""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            return self.tree.set(item, "node_id")
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about the displayed hierarchy."""
        if not self.hierarchy:
            return {}
        
        return {
            'hierarchy_name': self.hierarchy.name,
            'total_topics': len(self.hierarchy.topics),
            'total_nodes': sum(topic.get_node_count() for topic in self.hierarchy.topics.values()),
            'max_depth': max(topic.get_max_depth() for topic in self.hierarchy.topics.values()) if self.hierarchy.topics else 0
        }
    
    # Event handlers
    def _on_tree_selection(self, event):
        """Handle tree selection events."""
        selection = self.tree.selection()
        if selection and self.on_node_selected:
            item = selection[0]
            node_id = self.tree.set(item, "node_id")
            if node_id:
                self.on_node_selected(node_id)
    
    def _on_right_click(self, event):
        """Handle right-click events."""
        # Select the item under cursor
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.tree.focus(item)
            
            # Show context menu
            try:
                self.context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                self.context_menu.grab_release()
    
    def _on_double_click(self, event):
        """Handle double-click events."""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            # Toggle expansion
            if self.tree.item(item, "open"):
                self.tree.item(item, open=False)
            else:
                self.tree.item(item, open=True)
    
    # Context menu actions
    def _expand_all(self):
        """Expand all tree items."""
        for item in self._get_all_items():
            self.tree.item(item, open=True)
    
    def _collapse_all(self):
        """Collapse all tree items."""
        for item in self._get_all_items():
            self.tree.item(item, open=False)
    
    def _view_details(self):
        """View details of selected item."""
        selection = self.tree.selection()
        if selection and self.on_node_selected:
            item = selection[0]
            node_id = self.tree.set(item, "node_id")
            if node_id:
                self.on_node_selected(node_id)
    
    def _copy_node_id(self):
        """Copy node ID to clipboard."""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            node_id = self.tree.set(item, "node_id")
            if node_id:
                self.tree.clipboard_clear()
                self.tree.clipboard_append(node_id)
    
    # Utility methods
    def _get_all_items(self):
        """Get all items in the tree recursively."""
        def get_children(item):
            children = self.tree.get_children(item)
            for child in children:
                yield child
                yield from get_children(child)
        
        # Get all top-level items and their children
        for item in self.tree.get_children():
            yield item
            yield from get_children(item)
    
    def _configure_tags(self):
        """Configure tree item tags for styling."""
        self.tree.tag_configure("hierarchy", foreground="blue", font=("TkDefaultFont", 10, "bold"))
        self.tree.tag_configure("topic", foreground="green", font=("TkDefaultFont", 9, "bold"))
        self.tree.tag_configure("node", foreground="black")
        self.tree.tag_configure("highlight", background="yellow")
        self.tree.tag_configure("selected", background="lightblue")


if __name__ == "__main__":
    # Test the HierarchyExplorer
    root = tk.Tk()
    root.title("Hierarchy Explorer Test")
    root.geometry("600x400")
    
    def on_node_selected(node_id):
        print(f"Selected node: {node_id}")
    
    explorer = HierarchyExplorer(root, on_node_selected)
    
    # Create test hierarchy
    from ..models.hierarchy import Hierarchy
    hierarchy = Hierarchy("Test Hierarchy")
    hierarchy.create_topic_from_layers(["AI", "Machine Learning", "Deep Learning"])
    hierarchy.create_topic_from_layers(["Data Science", "Statistics", "Descriptive"])
    
    explorer.load_hierarchy(hierarchy)
    
    root.mainloop()
