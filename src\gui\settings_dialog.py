# Settings Dialog for application configuration
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from typing import Dict, Any, Optional
import os
from pathlib import Path

from ..config.settings import settings
from ..utils.logging_config import get_logger

logger = get_logger("gui.settings_dialog")


class SettingsDialog:
    """Dialog for editing application settings."""
    
    def __init__(self, parent):
        self.parent = parent
        self.settings_copy = {}
        self.changes_made = False
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Settings")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self._center_dialog()
        
        # Load current settings
        self._load_settings()
        
        # Setup UI
        self._setup_ui()
        
        # Handle dialog close
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_close)
        
        logger.debug("SettingsDialog created")
    
    def _center_dialog(self):
        """Center the dialog on the parent window."""
        self.dialog.update_idletasks()
        
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        dialog_width = self.dialog.winfo_reqwidth()
        dialog_height = self.dialog.winfo_reqheight()
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
    
    def _load_settings(self):
        """Load current settings into local copy."""
        self.settings_copy = {
            'app': {
                'name': settings.get('app', 'name', 'Deep Research Tool'),
                'debug': settings.get('app', 'debug', False),
                'auto_save': settings.get('app', 'auto_save', True),
                'theme': settings.get('app', 'theme', 'default')
            },
            'ollama': {
                'base_url': settings.get('ollama', 'base_url', 'http://localhost:11434'),
                'default_model': settings.get('ollama', 'default_model', 'llama2'),
                'timeout': settings.get('ollama', 'timeout', 30),
                'max_retries': settings.get('ollama', 'max_retries', 3),
                'stream_responses': settings.get('ollama', 'stream_responses', True)
            },
            'paths': {
                'output_dir': settings.get('paths', 'output_dir', './output_results'),
                'log_dir': settings.get('paths', 'log_dir', './logs'),
                'config_dir': settings.get('paths', 'config_dir', './config')
            },
            'analysis': {
                'default_type': settings.get('analysis', 'default_type', 'recursive'),
                'max_depth': settings.get('analysis', 'max_depth', 5),
                'batch_size': settings.get('analysis', 'batch_size', 10),
                'enable_quality_metrics': settings.get('analysis', 'enable_quality_metrics', True)
            },
            'logging': {
                'level': settings.get('logging', 'level', 'INFO'),
                'file_logging': settings.get('logging', 'file_logging', True),
                'console_logging': settings.get('logging', 'console_logging', True),
                'max_file_size': settings.get('logging', 'max_file_size', '10MB')
            }
        }
    
    def _setup_ui(self):
        """Setup the dialog UI."""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create notebook for different setting categories
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Setup tabs
        self._setup_general_tab()
        self._setup_ollama_tab()
        self._setup_paths_tab()
        self._setup_analysis_tab()
        self._setup_logging_tab()
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        # Buttons
        ttk.Button(button_frame, text="Restore Defaults", 
                  command=self._restore_defaults).pack(side=tk.LEFT)
        
        ttk.Button(button_frame, text="Cancel", 
                  command=self._on_cancel).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Apply", 
                  command=self._on_apply).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="OK", 
                  command=self._on_ok).pack(side=tk.RIGHT, padx=(5, 0))
    
    def _setup_general_tab(self):
        """Setup the general settings tab."""
        frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(frame, text="General")
        
        # Application name
        ttk.Label(frame, text="Application Name:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.app_name_var = tk.StringVar(value=self.settings_copy['app']['name'])
        ttk.Entry(frame, textvariable=self.app_name_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # Debug mode
        self.debug_var = tk.BooleanVar(value=self.settings_copy['app']['debug'])
        ttk.Checkbutton(frame, text="Enable debug mode", variable=self.debug_var).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # Auto save
        self.auto_save_var = tk.BooleanVar(value=self.settings_copy['app']['auto_save'])
        ttk.Checkbutton(frame, text="Auto-save hierarchies", variable=self.auto_save_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=2)
        
        # Theme
        ttk.Label(frame, text="Theme:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.theme_var = tk.StringVar(value=self.settings_copy['app']['theme'])
        theme_combo = ttk.Combobox(frame, textvariable=self.theme_var, values=['default', 'dark', 'light'], state="readonly")
        theme_combo.grid(row=3, column=1, sticky=tk.W, padx=(10, 0))
    
    def _setup_ollama_tab(self):
        """Setup the Ollama settings tab."""
        frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(frame, text="Ollama")
        
        # Base URL
        ttk.Label(frame, text="Base URL:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.base_url_var = tk.StringVar(value=self.settings_copy['ollama']['base_url'])
        ttk.Entry(frame, textvariable=self.base_url_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # Default model
        ttk.Label(frame, text="Default Model:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.default_model_var = tk.StringVar(value=self.settings_copy['ollama']['default_model'])
        ttk.Entry(frame, textvariable=self.default_model_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # Timeout
        ttk.Label(frame, text="Timeout (seconds):").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.timeout_var = tk.IntVar(value=self.settings_copy['ollama']['timeout'])
        ttk.Spinbox(frame, from_=5, to=300, textvariable=self.timeout_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # Max retries
        ttk.Label(frame, text="Max Retries:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.max_retries_var = tk.IntVar(value=self.settings_copy['ollama']['max_retries'])
        ttk.Spinbox(frame, from_=0, to=10, textvariable=self.max_retries_var, width=10).grid(row=3, column=1, sticky=tk.W, padx=(10, 0))
        
        # Stream responses
        self.stream_var = tk.BooleanVar(value=self.settings_copy['ollama']['stream_responses'])
        ttk.Checkbutton(frame, text="Stream responses", variable=self.stream_var).grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # Test connection button
        ttk.Button(frame, text="Test Connection", command=self._test_ollama_connection).grid(row=5, column=0, columnspan=2, pady=10)
    
    def _setup_paths_tab(self):
        """Setup the paths settings tab."""
        frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(frame, text="Paths")
        
        # Output directory
        ttk.Label(frame, text="Output Directory:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.output_dir_var = tk.StringVar(value=self.settings_copy['paths']['output_dir'])
        ttk.Entry(frame, textvariable=self.output_dir_var, width=50).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Button(frame, text="Browse", command=lambda: self._browse_directory(self.output_dir_var)).grid(row=0, column=2, padx=(5, 0))
        
        # Log directory
        ttk.Label(frame, text="Log Directory:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.log_dir_var = tk.StringVar(value=self.settings_copy['paths']['log_dir'])
        ttk.Entry(frame, textvariable=self.log_dir_var, width=50).grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Button(frame, text="Browse", command=lambda: self._browse_directory(self.log_dir_var)).grid(row=1, column=2, padx=(5, 0))
        
        # Config directory
        ttk.Label(frame, text="Config Directory:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.config_dir_var = tk.StringVar(value=self.settings_copy['paths']['config_dir'])
        ttk.Entry(frame, textvariable=self.config_dir_var, width=50).grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Button(frame, text="Browse", command=lambda: self._browse_directory(self.config_dir_var)).grid(row=2, column=2, padx=(5, 0))
    
    def _setup_analysis_tab(self):
        """Setup the analysis settings tab."""
        frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(frame, text="Analysis")
        
        # Default analysis type
        ttk.Label(frame, text="Default Analysis Type:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.analysis_type_var = tk.StringVar(value=self.settings_copy['analysis']['default_type'])
        analysis_combo = ttk.Combobox(frame, textvariable=self.analysis_type_var, 
                                    values=['recursive', 'iterative'], state="readonly")
        analysis_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # Max depth
        ttk.Label(frame, text="Max Depth:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.max_depth_var = tk.IntVar(value=self.settings_copy['analysis']['max_depth'])
        ttk.Spinbox(frame, from_=1, to=10, textvariable=self.max_depth_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # Batch size
        ttk.Label(frame, text="Batch Size:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.batch_size_var = tk.IntVar(value=self.settings_copy['analysis']['batch_size'])
        ttk.Spinbox(frame, from_=1, to=100, textvariable=self.batch_size_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # Quality metrics
        self.quality_metrics_var = tk.BooleanVar(value=self.settings_copy['analysis']['enable_quality_metrics'])
        ttk.Checkbutton(frame, text="Enable quality metrics", variable=self.quality_metrics_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)
    
    def _setup_logging_tab(self):
        """Setup the logging settings tab."""
        frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(frame, text="Logging")
        
        # Log level
        ttk.Label(frame, text="Log Level:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.log_level_var = tk.StringVar(value=self.settings_copy['logging']['level'])
        level_combo = ttk.Combobox(frame, textvariable=self.log_level_var, 
                                 values=['DEBUG', 'INFO', 'WARNING', 'ERROR'], state="readonly")
        level_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # File logging
        self.file_logging_var = tk.BooleanVar(value=self.settings_copy['logging']['file_logging'])
        ttk.Checkbutton(frame, text="Enable file logging", variable=self.file_logging_var).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=2)
        
        # Console logging
        self.console_logging_var = tk.BooleanVar(value=self.settings_copy['logging']['console_logging'])
        ttk.Checkbutton(frame, text="Enable console logging", variable=self.console_logging_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=2)
        
        # Max file size
        ttk.Label(frame, text="Max File Size:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.max_file_size_var = tk.StringVar(value=self.settings_copy['logging']['max_file_size'])
        ttk.Entry(frame, textvariable=self.max_file_size_var, width=15).grid(row=3, column=1, sticky=tk.W, padx=(10, 0))
    
    def _browse_directory(self, var):
        """Browse for directory."""
        directory = filedialog.askdirectory(initialdir=var.get())
        if directory:
            var.set(directory)
    
    def _test_ollama_connection(self):
        """Test Ollama connection."""
        try:
            from ..ollama.api import get_ollama_client
            
            # Create client with current settings
            base_url = self.base_url_var.get()
            timeout = self.timeout_var.get()
            
            # Test connection (this would need to be implemented)
            messagebox.showinfo("Connection Test", "Connection test not yet implemented", parent=self.dialog)

        except Exception as e:
            messagebox.showerror("Connection Error", f"Failed to connect: {str(e)}", parent=self.dialog)
    
    def _restore_defaults(self):
        """Restore default settings."""
        if messagebox.askyesno("Restore Defaults", "Restore all settings to defaults?", parent=self.dialog):
            # Reset to default values
            self._load_default_settings()
            self._update_ui_from_settings()
            self.changes_made = True
    
    def _load_default_settings(self):
        """Load default settings."""
        self.settings_copy = {
            'app': {
                'name': 'Deep Research Tool',
                'debug': False,
                'auto_save': True,
                'theme': 'default'
            },
            'ollama': {
                'base_url': 'http://localhost:11434',
                'default_model': 'llama2',
                'timeout': 30,
                'max_retries': 3,
                'stream_responses': True
            },
            'paths': {
                'output_dir': './output_results',
                'log_dir': './logs',
                'config_dir': './config'
            },
            'analysis': {
                'default_type': 'recursive',
                'max_depth': 5,
                'batch_size': 10,
                'enable_quality_metrics': True
            },
            'logging': {
                'level': 'INFO',
                'file_logging': True,
                'console_logging': True,
                'max_file_size': '10MB'
            }
        }
    
    def _update_ui_from_settings(self):
        """Update UI controls from settings."""
        # General tab
        self.app_name_var.set(self.settings_copy['app']['name'])
        self.debug_var.set(self.settings_copy['app']['debug'])
        self.auto_save_var.set(self.settings_copy['app']['auto_save'])
        self.theme_var.set(self.settings_copy['app']['theme'])
        
        # Ollama tab
        self.base_url_var.set(self.settings_copy['ollama']['base_url'])
        self.default_model_var.set(self.settings_copy['ollama']['default_model'])
        self.timeout_var.set(self.settings_copy['ollama']['timeout'])
        self.max_retries_var.set(self.settings_copy['ollama']['max_retries'])
        self.stream_var.set(self.settings_copy['ollama']['stream_responses'])
        
        # Paths tab
        self.output_dir_var.set(self.settings_copy['paths']['output_dir'])
        self.log_dir_var.set(self.settings_copy['paths']['log_dir'])
        self.config_dir_var.set(self.settings_copy['paths']['config_dir'])
        
        # Analysis tab
        self.analysis_type_var.set(self.settings_copy['analysis']['default_type'])
        self.max_depth_var.set(self.settings_copy['analysis']['max_depth'])
        self.batch_size_var.set(self.settings_copy['analysis']['batch_size'])
        self.quality_metrics_var.set(self.settings_copy['analysis']['enable_quality_metrics'])
        
        # Logging tab
        self.log_level_var.set(self.settings_copy['logging']['level'])
        self.file_logging_var.set(self.settings_copy['logging']['file_logging'])
        self.console_logging_var.set(self.settings_copy['logging']['console_logging'])
        self.max_file_size_var.set(self.settings_copy['logging']['max_file_size'])
    
    def _collect_settings(self):
        """Collect settings from UI controls."""
        self.settings_copy = {
            'app': {
                'name': self.app_name_var.get(),
                'debug': self.debug_var.get(),
                'auto_save': self.auto_save_var.get(),
                'theme': self.theme_var.get()
            },
            'ollama': {
                'base_url': self.base_url_var.get(),
                'default_model': self.default_model_var.get(),
                'timeout': self.timeout_var.get(),
                'max_retries': self.max_retries_var.get(),
                'stream_responses': self.stream_var.get()
            },
            'paths': {
                'output_dir': self.output_dir_var.get(),
                'log_dir': self.log_dir_var.get(),
                'config_dir': self.config_dir_var.get()
            },
            'analysis': {
                'default_type': self.analysis_type_var.get(),
                'max_depth': self.max_depth_var.get(),
                'batch_size': self.batch_size_var.get(),
                'enable_quality_metrics': self.quality_metrics_var.get()
            },
            'logging': {
                'level': self.log_level_var.get(),
                'file_logging': self.file_logging_var.get(),
                'console_logging': self.console_logging_var.get(),
                'max_file_size': self.max_file_size_var.get()
            }
        }
    
    def _apply_settings(self):
        """Apply settings to the configuration."""
        self._collect_settings()
        
        try:
            # Update settings (this would need to be implemented in settings module)
            for section, values in self.settings_copy.items():
                for key, value in values.items():
                    settings.set(section, key, value)
            
            # Save settings
            settings.save()
            
            self.changes_made = False
            logger.info("Settings applied successfully")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply settings: {str(e)}", parent=self.dialog)
            logger.error(f"Settings apply error: {str(e)}", exc_info=True)
    
    def _on_ok(self):
        """Handle OK button."""
        self._apply_settings()
        self.dialog.destroy()
    
    def _on_apply(self):
        """Handle Apply button."""
        self._apply_settings()
    
    def _on_cancel(self):
        """Handle Cancel button."""
        if self.changes_made:
            if messagebox.askyesno("Unsaved Changes", "Discard unsaved changes?", parent=self.dialog):
                self.dialog.destroy()
        else:
            self.dialog.destroy()
    
    def _on_close(self):
        """Handle dialog close."""
        self._on_cancel()
    
    def show(self):
        """Show the dialog (blocking)."""
        self.dialog.wait_window()


if __name__ == "__main__":
    # Test the SettingsDialog
    root = tk.Tk()
    root.title("Settings Dialog Test")
    root.geometry("300x200")
    
    def show_settings():
        dialog = SettingsDialog(root)
        dialog.show()
    
    ttk.Button(root, text="Show Settings", command=show_settings).pack(pady=50)
    
    root.mainloop()
